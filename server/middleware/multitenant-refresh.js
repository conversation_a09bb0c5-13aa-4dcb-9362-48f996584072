/**
  *  @module Multitenant	middleware    (alternative to auth.js)
  *
  *  Setup tenant-code in LB Context from request Header for refresh token
  */

//  Module Dependencies
const
	{ Context } = require('@perkd/multitenant-context'),
	{ Apis } = require('@crm/types'),
	{ LRUCache } = require('lru-cache'),
	crypto = require('crypto'),
	debug = require('debug')('multitenant:refresh')

const { Headers, Parameters } = Apis,
	{ AUTHORIZATION, X_ACCESS_TOKEN, TENANT, USER, TIMEZONE } = Headers,
	{ ACCESS_TOKEN, TENANT: TENANTCODE } = Parameters

// Cache for tenant tokens with 15 minute TTL
const tokenCache = new LRUCache({
	max: 1000,
	ttl: 1000 * 60 * 15, // 15 minutes
	updateAgeOnGet: true,
	ttlAutopurge: true
})

function parseAuthorization(string) {
	return string.replace(X_ACCESS_TOKEN, '').replace(/\s/g, '')
}

function getTenantAndSetContext(token, secretKey, requestId, options = {}) {
	const cacheKey = `${token}:${secretKey}`

	debug(`[${requestId}] Checking token cache for refresh token: ${cacheKey.substring(0, 10)}...`)
	let tenant = tokenCache.get(cacheKey)

	if (!tenant) {
		debug(`[${requestId}] Token cache miss, setting context with refresh token`)
		// For refresh tokens we allow expired tokens
		const result = Context.setWithToken(token, secretKey, {
			allowExpired: options.allowExpired || true
		})

		Context.accessToken = ''
		if (!(result instanceof Error)) {
			const { tenant: tenantCode, user, app, timezone } = result
			tenant = { tenantCode, user, app, timezone }
			debug(`[${requestId}] Caching refresh tenant: ${tenantCode}`)
			tokenCache.set(cacheKey, tenant)
		}
		else {
			debug(`[${requestId}] Refresh token validation failed: ${result.message}`)
			return result
		}
	}
	else {
		// Set context values when using cached tenant
		// Context.accessToken = token
		const { tenantCode, user, timezone } = tenant
		debug(`[${requestId}] Token cache hit, directly setting context values for tenant: ${tenantCode}`)
		Context.setValues(tenantCode, user, timezone)
	}

	debug(`[${requestId}] Current context tenant after setting: ${Context.tenant}`)
	return tenant
}

module.exports = function(options) {
	return async function injectTenantForRefresh(req, res, next) {
		// Generate unique ID for this request to trace through logs
		const requestId = crypto.randomBytes(4).toString('hex')
		req.requestId = requestId

		const { app, headers = {}, query = {} } = req,
			{ secretKey } = app.service

		debug(`[${requestId}] ====== REFRESH REQUEST START ======`)
		debug(`[${requestId}] URL: ${req.method} ${req.url}`)

		try {
			// Create a fresh context for this request
			const requestContext = Context.createContext({})

			// Run the entire request handling in the isolated context
			await Context.runInContext(requestContext, async () => {
				if (!Context.getCurrentContext()) {
					debug(`[${requestId}] Failed to get context`)
					const error = new Error('Failed to get context')
					return next(error)
				}

				// Handle direct tenant code - priority over token
				if (headers[TENANT] || (query && query[TENANTCODE])) {
					const tenantCode = (headers[TENANT] || query[TENANTCODE]).toLowerCase()
					const timezone = headers[TIMEZONE] || Context.timezone

					debug(`[${requestId}] Direct tenant code provided for refresh: ${tenantCode}`)

					let user = headers[USER] || Context.user || null
					if (typeof user === 'string') {
						try {
							user = JSON.parse(user)
						}
						catch (error) {
							debug(`[${requestId}] Error parsing user: ${error.message}`)
							user = null
						}
					}

					debug(`[${requestId}] Setting context values for tenant: ${tenantCode}`)
					Context.setValues(tenantCode, user, timezone)

					req.accessToken = { userId: user?.id || null }
					return next()
				}

				// Handle token-based authentication
				let accessToken = (req.body && req.body[ACCESS_TOKEN])
					|| (query && query[ACCESS_TOKEN]) || headers[X_ACCESS_TOKEN]

				if (!accessToken && headers[AUTHORIZATION]) {
					const authorization = headers[AUTHORIZATION]
					if (typeof authorization === 'string') {
						const splited = authorization.split(' ')
						if (splited.length === 2 && splited[0] === X_ACCESS_TOKEN) {
							accessToken = splited[1]
						}
					}
				}

				if (accessToken) {
					console.log('debug: ', accessToken)
					debug(`[${requestId}] Processing with refresh token: ${accessToken.substring(0, 10)}...`)

					const result = getTenantAndSetContext(accessToken, secretKey, requestId, { allowExpired: true })

					if (result instanceof Error) {
						if (options[TENANT] && options[TENANT] !== 'trap') {
							debug(`[${requestId}] Using default tenant: ${options[TENANT]}`)
							Context.tenant = options[TENANT].toLowerCase()
							return next()
						}

						result.stack = undefined
						result.status = 401
						debug(`[${requestId}] Invalid refresh token, returning 401`)
						return next(result)

					}

					const userId = result.user?.id || null
					debug(`[${requestId}] Refresh token authentication successful, tenant: ${result.tenantCode}, userId: ${userId}`)
					req.accessToken = { userId }
					return next()
				}

				// No tenant information provided
				if (options[TENANT] && options[TENANT] !== 'trap') {
					debug(`[${requestId}] No tenant info, using default: ${options[TENANT]}`)
					Context.tenant = options[TENANT].toLowerCase()
					return next()
				}

				debug(`[${requestId}] No tenant information provided for refresh`)
				const error = new Error('Tenant information required')
				error.status = 401
				return next(error)
			})
		}
		catch (error) {
			debug(`[${requestId}] Error in injectTenantForRefresh: ${error.message}`)
			error.status = error.status || 500
			next(error)
		}
	}
}
