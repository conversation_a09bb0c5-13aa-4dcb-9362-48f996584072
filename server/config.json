{"restApiRoot": "/api", "host": "0.0.0.0", "port": 8150, "remoting": {"rest": {"normalizeHttpPath": false, "handleErrors": false, "xml": false}, "json": {"strict": false, "limit": "100kb"}, "urlencoded": {"extended": true, "limit": "100kb"}}, "service": {"name": "Account", "domain": "xaccount", "version": "1.0.0", "description": "", "appPath": "lib/", "settings": ["settings"], "dependencies": {}, "autoStart": true, "canTerminate": true, "state": {"now": 0, "text": "", "since": ""}, "multitenancy": false, "tenantCode": "perkd"}, "modules": {"metrics": {"enabled": true}, "eventbus": {"enabled": true}, "sync": {"enabled": true}, "watchdog": {"enabled": true, "cloudWatch": {"awsRegion": "us-west-2"}}}, "apiRequest": {"version": "1.0.0", "apiVersion": 0, "timeout": 60000, "https": false, "host": "", "port": "", "apiRoot": "/api", "multitenancy": false}}