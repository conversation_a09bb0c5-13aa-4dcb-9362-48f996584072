/**
 *  @module Mixin:AppApi
 */

const { Context } = require('@perkd/multitenant-context'),
	{ Messagings, Touchpoints, Wallet } = require('@crm/types'),
	{ propertiesOf } = require('@perkd/sync'),
	{ bm, isEmptyObj, sameDay, parsePhoneNumber, TIMEZONE, isIP } = require('@perkd/utils'),
	{ ACCOUNT: ACCOUNT_ERR } = require('@perkd/errors/dist/app')

const { ACCOUNT_MOBILE_REQUIRED, ACCOUNT_MOBILE_INVALID, ACCOUNT_MOBILE_REGISTERED, ACCOUNT_MOBILE_IN_USE, ACCOUNT_MOBILE_SAME,
		ACCOUNT_MOBILE_NOT_REGISTERED, ACCOUNT_PASSWORD_NOT_MATCH, ACCOUNT_PASSWORD_INVALID, ACCOUNT_VCODE_INVALID,
	} = ACCOUNT_ERR,
	{ SMS, VOICE, PUSH, EMAIL, WHATSAPP } = Messagings.Service,
	{ PERKD } = Touchpoints.Type,
	{ Installations } = Wallet,
	{ ONLINE, OFFLINE, SUSPEND } = Installations.State,
	CHANNEL = {
		sms: SMS,
		voice: VOICE,
		push: PUSH,
		email: EMAIL,
		whatsapp: WHATSAPP
	},
	STATE = {
		// for Installation
		online: ONLINE,
		offline: OFFLINE,
		suspend: SUSPEND,
	},
	STATUS = { ALLOWED: 'allowed' },
	SYNC = 'sync',
	{ app: APP, voiceCall: VOICECALL } = appSettings(),
	THROUGH = { type: PERKD },
	ATTEMPT = {
		MAX: 5,
		SMS: {
			WHITELISTED: 1000,
			ROW: 5,
		},
		TTL: ********, // 1 day
	}

class Device {
	#exp

	#numbers = []

	get expired() {
		return this.#exp ? new Date() > this.#exp : false
	}

	get times() {
		return this.#numbers.length
	}

	add(number) {
		if (!this.#numbers.includes(number)) this.#numbers.push(number)
	}

	attempt(number) {
		this.#exp = new Date(new Date().getTime() + ATTEMPT.TTL)
		this.add(number)
		return this.times <= ATTEMPT.MAX
	}
}

const Attempt = {
	devices: {},
	mapping: {},

	check(install, number, carrier, suspicious) {
		const { id, device } = install || {}

		this.devices[id] = this.devices[id] || new Device()
		if (!this.devices[id].attempt(number)) return false

		if (suspicious) {
			const { name: carrierName = '' } = carrier || {},
				{ model = '' } = device || {},
				modelWithCarrier = `${carrierName}${model}`

			if (modelWithCarrier) {
				this.mapping[modelWithCarrier] = this.mapping[modelWithCarrier] || []
				if (!this.mapping[modelWithCarrier].includes(id)) this.mapping[modelWithCarrier].push(id)

				this.devices[modelWithCarrier] = this.devices[modelWithCarrier] || new Device()
				return this.devices[modelWithCarrier].attempt(number)
			}
		}

		return true
	},

	done(id) {
		if (this.devices[id]) delete this.devices[id]

		for (const modelWithCarrier in this.mapping) {
			if (this.mapping[modelWithCarrier].includes(id)) {
				delete this.mapping[modelWithCarrier]
				delete this.devices[modelWithCarrier]
			}
		}

		this.houseKeep()
	},

	houseKeep() {
		const { devices } = this

		for (const id in devices) {
			const device = devices[id]
			if (device.expired) delete devices[id]
		}
	}
}

class Counter {
	#count = 0
	#lastAt

	constructor(country) {
		this.country = country
	}

	exceeded(max = ATTEMPT.SMS.ROW) {
		return this.#count >= max
	}

	increase() {
		const NOW = new Date()

		if (!sameDay(this.#lastAt || NOW, NOW, TIMEZONE)) {
			this.#count = 1
		}
		else {
			this.#count++
		}
		this.#lastAt = NOW
	}
}

const Limit = {
	counter: {},

	check(country, max) {
		this.counter[country] = this.counter[country] || new Counter(country)
		this.counter[country].increase()

		return !this.counter[country].exceeded(max)
	}
}

module.exports = function(Account) {

	// ----- Register & Verify -----
	// TODO: block registration for minors, determined using countrycode + birthdate

	/**
	 * Register new Account
	 * @param	{Object} profile
	 *			{String} mobile
	 * @param	{Object} options
	 *			{String} code
	 *			{Boolean} skipValidate
	 *          {Object} locale - from install
	 *          {Object} carrier - from install
	 * 			{String} channel - what channel user can be reached for verification code
	 * @return	{Object} { token: "JWT access token", openId: "Cognito token", voiceCall: true }
	 */
	Account.register = async function(profile = {}, options = {}) {
		const { app } = Account,
			{ Metric, Event } = app,
			{ Person, Installation } = app.models,
			{ mobile } = profile,
			REGISTER = Account.PURPOSE.register,
			ERROR_EVT = Event.account.error.registered,
			start = bm.mark(),
			NOW = new Date(),
			{ location, installation } = Context.appContext,
			{ carrier, skipValidate, channel } = options

		// ---  Validate mobile & handle blacklist
		if (!mobile) {
			return emitReject(ERROR_EVT, ACCOUNT_MOBILE_REQUIRED, profile, true, { metric: Metric.register.error, value: 1 })
		}

		const phoneNumber = parsePhoneNumber(mobile),
			{ fullNumber, countryCode, regionCode, isValidMobile } = phoneNumber,
			ctry = location?.countryCode || carrier?.country,
			country = (ctry && ctry !== '--') ? ctry : regionCode, // exlude invalid country ("--")
			voiceCall = VOICECALL.includes(countryCode),
			locale = {
				languages: options.locale?.languages,
				timeZone: options.locale?.timeZone,
				country
			},
			{ device } = installation || {},
			{ ip, model } = device || {},
			tags = { countryCode, country, device: model, ip },
			nullToken = { token: Account.token('', '') }

		// handle blacklist, return fake token to trick hackers
		const validIp = validateIP(ip),
			validDeviceId = /^[0-9a-fA-F-]+$/.test(installation.id)		// base-16 validation

		if (!validIp || !validDeviceId) {
			appMetric(Metric.register.blacklisted, 1, { tags })
			console.log('Account.register.attack.BlackListedDevice %j', { fullNumber, installation, countryCode, carrier })
			return nullToken
		}

		if (Account.isBlackListed(installation, countryCode, carrier)
			|| !Attempt.check(installation, fullNumber, carrier, Account.isSuspicious(installation, countryCode))) {

			const checkPerson = await Person.findByPerkdMobile(fullNumber),
				startup = checkPerson ? await Account.getStartupCard(checkPerson.id) : null // if user has startup, do not block

			if (!startup) {
				appMetric(Metric.register.blacklisted, 1, { tags })
				console.log('Account.register.attack.BlackListedDevice %j', { fullNumber, installation, countryCode, carrier })
				return nullToken
			}
		}

		if (!fullNumber || (!skipValidate && !isValidMobile)) {
			// phoneNumber.fullNumber is empty means the mobile is really invalid
			if (!fullNumber) {
				appNotify('register/invalid_mobile', { profile }, 'alert')
			}

			return emitReject(ERROR_EVT, ACCOUNT_MOBILE_INVALID, profile, true,
				{ metric: Metric.register.error, value: 1, tags: { country } })
		}

		// ---  Prevent flooding of fraudulent registration
		const { countries = [], os = [], limits = {} } = app.getSettings('whitelist') || {},
			whitelisted = countries.includes(Number(countryCode)), // getSettings will convert number strings to numbers
			max = limits[countryCode] ?? (whitelisted ? ATTEMPT.SMS.WHITELISTED : ATTEMPT.SMS.ROW)

		if (!Limit.check(countryCode, max)) {
			const checkPerson = await Person.findByPerkdMobile(fullNumber),
				startup = checkPerson ? await Account.getStartupCard(checkPerson.id) : null // if user has startup, do not block

			if (!startup) {
				appMetric(Metric.register.capExceeded, 1, { tags })
				console.log('Account.register.attack.CountryCapExceeded %j', { installation, countryCode, carrier })
				return nullToken
			}
		}

		// ---  Handle registration
		return Account.queue(fullNumber, async () => {
			const accounts = await Account.findByName(fullNumber, { multiple: true })

			let account, person

			if (accounts.length > 0) {	// 1. account exists
				console.log('debug [register] - 1: %j', accounts)
				const verified = accounts.find(a => a.verifiedAt)

				if (verified) {		// a. cannot register, already verified
					return emitReject(ERROR_EVT, ACCOUNT_MOBILE_REGISTERED, profile, true,
						{ metric: Metric.register.error, value: 1, tags: { country } })
				}

				// b. mobile has been registered but yet to be verified
				account = accounts.find(a => !a.verifiedAt)
				await account.updateAttributes({ registeredAt: new Date() })
				person = await Person.findById(account.personId)
			}
			else {	// 2. Mobile not registered, create Person & Account
				console.log('debug [register] - 2: %j')
				const [ user, install ] = await Promise.all([
						Person.findOrCreateByMobile(fullNumber, { locale, visible: true }),
						Installation.findById(installation.id).catch(err => { })
					]),
					{ accountId, personId } = install || {},
					newAccount = {
						name: fullNumber,
						personId: user.id,
						registeredAt: new Date(),
					}
				console.log('debug [register] - 2-2: %j', { user, install })
				person = user
				account = await Account.create(newAccount)
				console.log('debug [register] - 2-3: %j', { account })
				if (accountId) {
					// remove previous unverified Account and Person (when installation refreshes)
					console.log('debug [register] - 2-4: %j', { accountId, personId })
					await deleteOrphans(accountId, personId)
				}
			}

			// update installation async
			installation.personId = person.id.toString()
			installation.accountId = account.id.toString()
			// additional prop for A3 (A3 does not launch outside)
			installation.state = STATE.online
			installation.stateSince = new Date()
			installation.lastSeenAt = NOW
			Installation.refresh(installation)

			// request old verion users to upgrade
			const purpose = await Account.promptUpgrade(account.id, installation, REGISTER, countryCode)
			if (!purpose) return

			// generate tokens, send verification code & sync person data
			const opt = { locale, code: options.code, countryPrefix: countryCode },
				stats = { metric: Metric.register.error, value: 1, tags: { country } },
				verificationChannel = app.getSettings('verificationChannel'),
				preferredChannel = verificationChannel[countryCode] || (channel === WHATSAPP ? WHATSAPP : SMS),
				[ tokens, personData, { code } ] = await Promise.all([
					account.getTokens({ skipValidate: true }),
					propertiesOf(person),
					account.sendVerifyCode(REGISTER, CHANNEL[preferredChannel], opt)
						.catch(error => {
							console.log('debug [register] %j', { error, profile, options, tenant: Context.tenant })
							emitReject(ERROR_EVT, error.code, profile, true, stats)
						})
				]),
				{ token, openId } = tokens

			account.emitEvent('registered', { code })
			appMetric(Metric.register.count, 1, { tags: { country, os: installation?.os?.name, app: installation?.app?.version } })
			appMetric(Metric.register.latency, bm.diff(start), { tags: { country } })

			return {
				token, openId, voiceCall,
				toChangePassword: true,
				sync: [ personData ],
				channel: preferredChannel
			}
		})
	}

	/**
	 * @param 	{String} code verfication code received after registration
	 * @return	{Object} { sync: [card] } - startup card
	 */
	Account.verify = async function(code) {
		const { app } = Account,
			{ Metric, Event, models } = app,
			start = bm.mark(),
			{ AccountSetting, Installation, Person, Card } = models,
			NOW = new Date(),
			{ installation, location = {} } = Context.appContext,
			country = location.countryCode

		return Account.queue(installation.personId, async () => {
			const account = await Account.get(),
				{ name, personId } = account,
				verifyCode = account._verifyCode(code, Account.PURPOSE.register)

			// check if the verify code is correct
			if (!verifyCode) {
				return emitReject(Event.account.error.verified, ACCOUNT_VCODE_INVALID, account, true,
					{ metric: Metric.verify.error, value: 1 })
			}

			await Account.notVerified(name)
			await AccountSetting.init(account)

			// update installation async
			installation.state = STATE.online
			installation.stateSince = new Date()
			installation.loggedIn = true
			installation.lastSeenAt = new Date()
			Installation.upsert(installation)

			const permissionList = [
					{ channel: 'serviceTerms', status: STATUS.ALLOWED, grantedAt: NOW },
					{ channel: 'privacyPolicy', status: STATUS.ALLOWED, grantedAt: NOW },
				],
				changes = {
					password: code,		// default to verification code
					verifiedAt: NOW,
					lastLoggedInAt: NOW,
				}

			account.updateAttributes(changes).then(updated => {
				verifyCode.usedAt = NOW
				updated.updateVerifyCode(verifyCode)
					.catch(err => appNotify('[updateVerifyCode]', { err, accountId: updated.id }))
			})

			Person.doUpsert({ id: personId, permissionList }, null, THROUGH)
				.then(person => {
					appMetric(Metric.verify.count, 1, { tags: { country: person.phoneList[0].regionCode, registeredAt: account.registeredAt.toISOString() } })
					appMetric(Metric.verify.latency, bm.diff(start), { tags: { country } })
				})

			Account.issuePerkdID(personId)
			Attempt.done(installation.id)
			account.emitEvent('verified')

			const startup = await Account.getStartupCard(personId)
			return startup ? { sync: [ propertiesOf(startup) ] } : {}
		})
	}

	/**
	 * Request Verification Code
	 *		NOTE:	If user change mobile when request vcode, there will be a new account generated
	 *				So when user request verify code, the mobile number should be same with account name
	 * @param  {String} mobile new mobile no.
	 * @param  {String} channel channel to send vcode
	 * @param  {String} purpose (see Account.PURPOSE)
	 * @param  {Object} options
	 * 		   {Object} locale - from install
	 * @return {Promise<void>}
	 */
	Account.requestVerifyCode = async function(mobile, channel = CHANNEL.sms, purpose = Account.PURPOSE.register, options = {}) {
		const { Event } = Account.app,
			{ locale } = options,
			{ requestedVCode, requestedVCode2 } = Event.account.error,
			isRegister = (purpose === Account.PURPOSE.register),
			EVENT = isRegister ? 'requestedVCode' : 'requestedVCode2',
			ERROR_EVT = isRegister ? requestedVCode : requestedVCode2,
			REQUESTED_AT = new Date(),
			phoneNumber = parsePhoneNumber(mobile),
			{ fullNumber, countryCode, isPossibleMobile } = phoneNumber,
			sendOptions = {
				mobile: fullNumber,
				countryPrefix: countryCode,
				locale,
				resend: true,
			},
			account = await Account.get()

		// find the account which requested to resend the verify code
		if (!isPossibleMobile) {
			return emitReject(ERROR_EVT, ACCOUNT_MOBILE_INVALID, isRegister ? account : { ...account, newMobile: mobile })
		}

		// validate resend mobile, may differ from the one registered
		// check if the mobile has been registered & verified under another account
		await Account.notVerified(fullNumber)

		const { code } = await account.sendVerifyCode(purpose, channel, sendOptions)
			.catch(err => emitReject(ERROR_EVT, err.code, isRegister ? account : { ...account, newMobile: fullNumber }))

		account.emitEvent(EVENT, { newMobile: fullNumber, code, requestedAt: REQUESTED_AT })
	}

	// ----- Authenticate-----

	/**
	 * @param  {Object} install - entire installation
	 * @param  {Date} at - launched time
	 * @return {Object} { token, openId, latestVersion }
	 */
	Account.launch = async function(install, at = new Date()) {
		const { app } = Account,
			{ Metric, Event, models } = app,
			{ Installation, Person } = models,
			NOW = new Date(),
			launchedAt = at,
			{ personId } = Context.user,
			{ location } = Context.appContext,
			{ os, locale } = install

		install.loggedIn = true
		install.state = STATE.online
		install.stateSince = NOW
		install.lastSeenAt = NOW
		app.emit(Event.account.launched, { personId, install, location, launchedAt })

		if (isEmptyObj(install.tokens)) delete install.tokens
		Installation.upsert(install)

		// check if app locale changed then update person locale
		if (personId) {
			const person = await Person.findById(personId)

			if (person) {
				const personLocale = person.toJSON().locale,
					{ version } = install.app

				if (!sameLocale(locale, personLocale)) {
					if (version >= '4') {	// from A4 onwards managed by Person svc onPersonRoam handler
						delete locale.country
					}
					Person.doUpsert({ id: person.id, locale: { ...personLocale, ...locale } }, null, THROUGH)
				}
			}
		}

		const { token, openId } = await Account.refreshToken(),
			{ version: latestVersion } = APP[os.name][0]

		appMetric(Metric.launch.app.count, 1, { tags: { country: locale.country } })
		// appMetric(Metric.launch.app.latency, bm.diff(start), { tags: { country: locale.country } })

		return { token, openId, latestVersion }
	}

	/**
	 * @param	{String} mobileNumber
	 * @param 	{String} password
	 * @param	{Boolean} touchID
	 * @param	{String} accountId - for A3 upgraded
	 * @return	{Object} { token, openId, toChangePassword }
	 */
	Account.login = async function(mobileNumber, password, touchID, accountId) {
		const { app } = Account,
			{ Metric, Event } = app,
			start = bm.mark(),
			country = mobileNumber ? parsePhoneNumber(mobileNumber).regionCode : undefined,
			{ Installation } = app.models,
			NOW = new Date(),
			{ installation } = Context,
			tags = { country }

		if (!touchID) {
			const { device, id: deviceId } = installation || {},
				{ ip } = device || {}

			// basic validation, combine with same logic in register
			const validIp = validateIP(ip),
				validDeviceId = /^[0-9a-fA-F-]+$/.test(deviceId),		// base-16 validation
				validMobile = /^\+?\d+$/.test(mobileNumber)

			if (!validIp || !validDeviceId || !validMobile) {
				tags.reason = !validIp ? 'InvalidIp' : !validDeviceId ? 'InvalidDeviceId' : 'InvalidMobile'
				appMetric(Metric.login.auth.error, 1, { tags })
				console.log('Account.login.attack.BlackListedDevice %j', { mobileNumber, installation, country })
				return Account.rejectErr(ACCOUNT_PASSWORD_INVALID)
			}

			if (!Attempt.check(installation, mobileNumber)) {
				console.log('Account.login.attack.BlackListedDevice %j', { mobileNumber, installation })
				appMetric(Metric.login.auth.error, 1, { tags: { reason: 'blackListed' } })
				return Account.rejectErr(ACCOUNT_PASSWORD_INVALID)
			}
		}

		if (!mobileNumber && !touchID && !accountId) {
			const evtData = { personId: installation.personId, mobile: mobileNumber }

			// account not found yet, not include in event
			return emitReject(Event.account.error.login, ACCOUNT_PASSWORD_INVALID, evtData,
				{ metric: Metric.login.auth.error, value: 1, tags })
		}

		const account = await Account.auth(mobileNumber, password, touchID, accountId).catch(err => {
				console.log('[login]', err)
				return Account.rejectErr(err.code)
			}),
			{ id, personId, options, limits = {} } = account,
			{ forceLogout } = options

		Attempt.done(installation.id)

		const install = await Installation.findById(installation.id)
		if (install) {
			const { accountId } = install

			//  update installation
			install.accountId = id
			install.personId = personId
			install.loggedIn = true
			install.state = STATE.online
			install.stateSince = NOW
			install.lastSeenAt = NOW
			Installation.refresh(install)

			// remove previous unverified Account and Person (when install refreshes)
			if (accountId && accountId !== installation.accountId) {
				deleteOrphans(accountId, installation.personId)
			}
		}

		if (forceLogout === true) {
			// TODO: logout all devices
			await account.updateAttributes({ 'options.forceLogout': false, lastLoggedInAt: NOW })
		}

		account.updateAttributes({ lastLoggedInAt: NOW })

		const { token, openId } = await account.getTokens({ skipValidate: true }),
			toChangePassword = !(limits.password?.changeCount)

		appMetric(Metric.login.auth.count, 1, { tags })
		appMetric(Metric.login.auth.latency, bm.diff(start), { tags })

		return { token, openId, toChangePassword }
	}

	/**
	 * Logout
	 * @return	{void}
	 */
	Account.logout = async function() {
		const { app } = Account,
			{ Installation } = app.models,
			NOW = new Date(),
			{ installation } = Context,
			{ accountId } = installation

		installation.loggedIn = false
		installation.lastSeenAt = NOW
		Installation.upsert(installation)

		const account = await Account.findById(accountId),
			{ options = {} } = account,
			context = { force: !!options.forceLogout }

		account.emitEvent('logout', { logoutAt: new Date(), context })
	}

	Account.refreshToken = async function() {
		console.log('requestContext [3] >>>>>>>>>>>>', Context.getCurrentContext().requestId)
		console.log('------------- 2', Context.accountId)
		const account = await Account.get()
		console.log('------------- account returned:', account)
		// Since Account.get() currently returns accountId string, let's just return success
		return { success: true, accountId: account }
		// return account.getTokens()
	}

	// -----  Mobile Number  -----

	/**
	 * Send SMS vCode to unverified newMobileNumber
	 * @param 	{String} newMobileNumber
	 * @param 	{Object} options
	 *			  {Boolean} skipValidate
	 *        	  {Object} locale - from install
	 * @return	{Promise<Object>} - { voiceCall: true }
	 */
	Account.changeMobile = async function(newMobileNumber, options = {}) {
		const { app } = Account,
			{ Event, models } = app,
			{ Installation } = models,
			{ locale, skipValidate } = options,
			CHANGE_MOBILE = Account.PURPOSE.changeMobile,
			ERROR_EVT = Event.account.error.changedMobile,
			{ fullNumber, countryCode, isValidMobile } = parsePhoneNumber(newMobileNumber),
			voiceCall = VOICECALL.includes(countryCode),
			requestedAt = new Date(),
			sendOptions = {
				mobile: fullNumber,
				countryPrefix: countryCode,
				locale,
			},
			{ installation: install } = Context,
			[ account, installation = {} ] = await Promise.all([
				Account.get(true),
				Installation.findById(install.id).catch(err => { })
			]),
			{ appList = [] } = installation,
			verificationChannel = app.getSettings('verificationChannel'),
			preferredChannel = verificationChannel[countryCode] || (appList.includes(WHATSAPP) ? WHATSAPP : SMS)

		if (!skipValidate && !isValidMobile) {
			return emitReject(ERROR_EVT, ACCOUNT_MOBILE_INVALID, { ...account.toJSON(), newMobile: newMobileNumber })
		}
		if (fullNumber === account.name) {
			return emitReject(ERROR_EVT, ACCOUNT_MOBILE_SAME, { ...account.toJSON(), newMobile: fullNumber })
		}

		// The target mobile cannot be registered by a verified account
		await Account.notVerified(fullNumber)
			.catch(() => emitReject(ERROR_EVT, ACCOUNT_MOBILE_IN_USE, { ...account.toJSON(), newMobile: fullNumber }))

		const { code } = await account.sendVerifyCode(CHANGE_MOBILE, CHANNEL[preferredChannel], sendOptions)
			.catch(err => emitReject(ERROR_EVT, err.code, { ...account.toJSON(), newMobile: fullNumber }))

		account.emitEvent('changedMobile', { code, newMobile: fullNumber, requestedAt })

		return { voiceCall, channel: preferredChannel }
	}

	/**
	 * Verify Change of Mobile number
	 * @param	{String} newMobileNumber
	 * @param	{String} code
	 * @return {Object}
	 */
	Account.verifyChangeMobile = async function(newMobileNumber, code) {
		const { models, Event } = Account.app,
			{ Person } = models,
			CHANGE_MOBILE = Account.PURPOSE.changeMobile,
			ERROR_EVT = Event.account.error.verifiedMobileChange,
			phoneNumber = parsePhoneNumber(newMobileNumber),
			{ fullNumber, isPossibleMobile } = phoneNumber,
			account = await Account.get(true),
			{ name, limits } = account,
			oldMobile = name,
			newMobile = fullNumber,
			changedAt = new Date()

		if (!isPossibleMobile) {
			return emitReject(ERROR_EVT, ACCOUNT_MOBILE_INVALID, { ...account.toJSON(), mobile: newMobileNumber })
		}

		await Account.notVerified(newMobile)

		// get verification code
		const verifyCode = account._verifyCode(code, CHANGE_MOBILE, newMobile)
		if (!verifyCode) {
			return emitReject(ERROR_EVT, ACCOUNT_VCODE_INVALID, { ...account.toJSON(), mobile: fullNumber })
		}

		// update account name
		const changes = {
			name: newMobile,
			'limits.mobile.changeCount': timesMobileChanged(limits) + 1,
			'limits.mobile.lastChangedAt': changedAt,
		}

		account.updateAttributes(changes).then(updated => {
			verifyCode.usedAt = changedAt
			updated.updateVerifyCode(verifyCode)
				.catch(err => {
					console.log('[verifyChangeMobile]', { err, oldMobile })
				})
			account.emitEvent('verifiedMobileChange', { oldMobile, changedAt })
		})

		// update old mobile number to new number
		try {
			const person = await Person.findById(account.personId)
			return person.replacePhone(oldMobile, account.name, { through: THROUGH })
		}
		catch (err) {
			appLog('verifyChangeMobile', { mobile: account.name, error: err })
		}
	}

	// -----  Password  -----

	/**
	 * @param	{String} mobileNumber
	 * @param	{String} channel
	 * @param	{Boolean} options
	 * @return	{Object}
	 * 			{Object} channel: { sms: true, email: true, voice: true } - password sent channels
	 *			{Boolean} voiceCall: true - indicate whether allow request password via call
	 */
	Account.requestPassword = async function(mobileNumber, channel, options = {}) {
		const { app } = Account,
			{ Metric, Event } = app,
			{ locale } = options,
			ERROR_EVT = Event.account.error.requestedPSWD,
			phoneNumber = parsePhoneNumber(mobileNumber),
			{ fullNumber, countryCode, regionCode, isPossibleMobile } = phoneNumber,
			voiceCall = VOICECALL.includes(countryCode),
			{ installation } = Context,
			at = new Date(),
			verificationChannel = app.getSettings('verificationChannel'),
			preferredChannel = verificationChannel[countryCode] || (channel === WHATSAPP ? WHATSAPP : SMS),
			sendOptions = {
				countryPrefix: countryCode,
				resend: true,
				channel: preferredChannel,
				locale,
			},
			filter = {
				include: {
					relation: 'person',
					scope: { fields: [ 'id', 'name', 'locale' ] },
				},
			}

		if (!isPossibleMobile) {
			return emitReject(ERROR_EVT, ACCOUNT_MOBILE_INVALID, { personId: installation.personId, mobile: mobileNumber })
		}

		const account = await Account.isVerified(fullNumber, filter)
			.catch(() => emitReject(ERROR_EVT, ACCOUNT_MOBILE_NOT_REGISTERED, { mobile: fullNumber })) // have not login, no related person

		const res = await account.sendPassword(fullNumber, at, sendOptions)
			.catch(error => {
				console.log('debug [request pwd error] %j', error)
				return emitReject(ERROR_EVT, error.code, { personId: account.personId, mobile: fullNumber })
			})

		res.voiceCall = voiceCall
		appMetric(Metric.password.request, 1, { tags: { country: regionCode } })

		return res
	}

	/**
	 * Change Password
	 * @param	{String} newPassword
	 * @param	{String} oldPassword
	 * @param	{Boolean} ignoreOld
	 * @return	{Account}
	 */
	Account.changePassword = async function(newPassword, oldPassword, ignoreOld = false) {
		const { app } = Account,
			{ Event } = app,
			account = await Account.get(true),
			{ password, limits } = account || {}

		if (!ignoreOld && oldPassword !== password) {
			return emitReject(Event.account.error.changedPSWD, ACCOUNT_PASSWORD_NOT_MATCH, account)
		}

		const changedAt = new Date(),
			changes = {
				password: newPassword,
				'limits.password.changeCount': timesPasswordChanged(limits) + 1,
				'limits.password.lastChangedAt': changedAt,
			}

		await account.updateAttributes(changes)
		account.emitEvent('changedPSWD', { changedAt })
		return account
	}

	// -----  Private functions  -----

	function emitReject(evt, code, data, includeInstall = true, stats = {}) {
		const { app } = Account,
			install = includeInstall ? Context.installation : undefined,
			d = data.toJSON ? data.toJSON() : data,
			body = { ...d, error: { code }, install },
			{ metric, value, tags = {} } = stats

		if (metric) {
			appMetric(metric, value, { tags: { ...tags, error: code } })
		}
		app.emit(evt, body)
		return Account.rejectErr(code)
	}

	/**
	 * Delete orphaned account(unverified) & person
	 * @param	{String} accountId
	 * @param	{String} personId
	 * @return 	{Promise<void>}
	 */
	async function deleteOrphans(accountId = null, personId = null) {
		const { app } = Account,
			{ Person, Card } = app.models,
			account = await Account.findById(accountId)

		if (account && !account.verifiedAt) {
			account.delete()	// 1 to 1 Account-Person Relationship

			if (personId) {		// if person has card, do not delete, just clear installations
				const card = await Card.findOne({ where: { personId } })

				if (card) {
					await Person.doUpsert({ id: personId, installationIds: [], visible: false })
				}
				else {
					await Person.deleteById(personId)
				}
			}
		}
	}

	/**
	 * Locale comparator
	 * @param	{Object} currentLocale
	 * @param	{Object} locale
	 * @return	{Boolean} true for same locale
	 */
	function sameLocale(currentLocale, locale) {
		const sameLanguages = (currLang, lang) => {
			if (!currLang && !lang) return true
			if (!currLang && lang) return false
			if (currLang && !lang) return false
			if (currLang.length !== lang.length) return false

			for (let i = 0; i < lang.length; i++) {
				if (currLang[i] !== lang[i]) return false
			}
			return true
		}

		if (!sameLanguages(currentLocale.languages, locale.languages)) return false
		if (currentLocale.timeZone !== locale.timeZone) return false
		return true
	}

	function timesPasswordChanged(limits = {}) {
		return limits.password?.changeCount || 0
	}

	function timesMobileChanged(limits = {}) {
		return limits.mobile?.changeCount || 0
	}

	function validateIP(ip) {
		if (!ip) return true // allow empty IP
		return isIP(ip)
	};
}
