/**
 *  @module Mixin:Auth
 */

const { Context } = require('@perkd/multitenant-context'),
	{ parsePhoneNumber } = require('@perkd/utils'),
	{ ACCOUNT: ACCOUNT_ERR } = require('@perkd/errors/dist/app')

const { ACCOUNT_PASSWORD_INVALID, ACCOUNT_NOT_FOUND, ACCOUNT_MOBILE_NOT_VERIFIED } = ACCOUNT_ERR,
	STATE = {
		// for Installation
		offline: 'offline',
		suspend: 'suspend',
		online: 'online',
	},
	STATUS = { ALLOWED: 'allowed' },
	PERKD = 'perkd'

module.exports = function(Account) {

	/**
	 * @param	{String} mobileNumber
	 * @param	{String} password
	 * @param	{Boolean} touchID
	 * @param	{String} accountId - for A3 upgraded
	 * @return	{Account} account
	 */
	Account.auth = async function(mobileNumber, password, touchID, accountId) {
		return accountId
			? withAccountId(accountId)
			: (touchID ? withBiometric() : withMobile(mobileNumber, password))
	}

	// -----  Private functions  -----

	/**
	 * Login with accountId
	 * @return	{Account} account
	 */
	async function withAccountId(accountId = null) {
		const NOW = new Date(),
			account = await Account.findById(accountId)

		if (!account) {
			return Account.rejectErr(ACCOUNT_NOT_FOUND)
		}
		if (!account.verifiedAt) {
			const { installation: install } = Context,
				{ app, os } = install,
				{ version } = app,
				ios = os.name === 'ios'

			if (!ios || version > '4.0.3') {
				return Account.rejectErr(ACCOUNT_MOBILE_NOT_VERIFIED)
			}

			/**
			 * patch for app v4.0-4.0.2 upgrade bug, return not_verified cannot redirect to login window
			 * continue to do verify, initial perference, refresh installation and issue perkdID card
			 */
			const { AccountSetting, Installation, Person } = Account.app.models,
				changes = {
					verifiedAt: NOW,
					lastLoggedInAt: NOW,
				},
				through = { type: PERKD },
				permissionList = [
					{ channel: 'serviceTerms', status: STATUS.ALLOWED, grantedAt: NOW },
					{ channel: 'privacyPolicy', status: STATUS.ALLOWED, grantedAt: NOW },
				]

			await AccountSetting.init(account)
			await account.updateAttributes(changes)

			Person.doUpsert({ id: account.personId, permissionList }, null, through)

			install.state = STATE.online
			install.stateSince = NOW
			install.loggedIn = true
			install.lastSeenAt = NOW
			Installation.upsert(install)

			await Account.issuePerkdID(account.personId)
			account.emitEvent('verified')
		}

		account.emitEvent('login', { channel: 'upgrade', loginAt: NOW })
		appNotify('login', { channel: 'upgrade', loginAt: NOW }, 'good', '-upgrade')

		return account
	}

	/**
	 * Login with Biometric (TouchID or FaceID)
	 * 	- app will keep the JWT token with Touch ID mode
	 * @return	{Account} account
	 */
	async function withBiometric() {
		const account = await Account.get(true)

		account.emitEvent('login', { channel: 'biometric', loginAt: new Date() })
		return account
	}

	/**
	 * Login with Mobile number & password
	 * @return	{Account} account
	 */
	async function withMobile(mobile = '', password = '') {
		const LOGIN_ERROR = Account.app.Event.account.error.login,
			phoneNumber = parsePhoneNumber(mobile),
			{ isPossibleMobile, fullNumber, countryCode: countryPrefix } = phoneNumber,
			filterOptions = {
				include: {
					relation: 'person',
					scope: { fields: [ 'id', 'name', 'locale' ] }
				}
			},
			loginAt = new Date(),
			account = await Account.isVerified(fullNumber, filterOptions)
				.catch(err => {
					const errorCode = err.code
					appMetric(Account.app.Metric.login.auth.error, 1, { tags: { reason: errorCode } })
					return emitReject(LOGIN_ERROR, errorCode, { mobile })
				})	// not login, activity will not get personId)

		// if (!isPossibleMobile) {
		// 	const { personId } = getInstall();
		// 	return emitReject(LOGIN_ERROR, ACCOUNT_MOBILE_INVALID, { personId, mobile });
		// }

		if (password !== account.password) {
			// User requested new password & login with it?
			// if password matched, update account password to the new one

			const verifyCode = account._verifyCode(password, Account.PURPOSE.password),
				changes = {
					password,
					limits: {
						...account.limits,
						password: { changeCount: 0, lastChangedAt: loginAt }
					},
					options: { ...account.options, forceLogout: false },
				}

			if (!verifyCode) {
				return emitReject(LOGIN_ERROR, ACCOUNT_PASSWORD_INVALID, { ...account.toJSON(), mobile, countryPrefix })
			}

			await account.updateAttributes(changes)
			verifyCode.usedAt = loginAt
			account.updateVerifyCode(verifyCode)
		}

		account.emitEvent('login', { channel: 'mobilePass', loginAt })
		return account
	}

	function emitReject(evt, code, data, includeInstall = true) {
		const { app } = Account,
			evtData = Object.assign({}, data.toJSON ? data.toJSON() : data, { error: { code } })

		app.emit(evt, includeInstall ? Object.assign(evtData, { install: Context.installation }) : evtData)
		return Account.rejectErr(code)
	}
}
