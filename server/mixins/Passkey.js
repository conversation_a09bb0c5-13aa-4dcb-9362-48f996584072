/**
 * @module Passkey - mixin
 * Passkey mixin for handling WebAuthn/Passkey functionality in Loopback models.
 * Implements WebAuthn/Passkey authentication following the FIDO2 specification.
 *
 * Features:
 * - Passkey registration (attestation)
 * - Passkey authentication (assertion)
 * - Passkey management (listing, removal)
 * - Counter validation for clone detection
 * - Transaction-safe updates
 *
 * Note on Counter Validation:
 * The signature counter is used to detect cloned authenticators.
 * - Counter validation allows equal values as some authenticators (especially platform authenticators) don't increment counters
 * - Only rejects if counter decreases, which indicates possible authenticator cloning
 * - Counter values are stored and updated per credential
 */

const { Binary } = require('mongodb'),
	{ Passkey, validateCredential, validateOrigin, errorResponse, ERRORS } = require('@perkd/passkey'),
	{ ACCOUNT: ACCOUNT_ERR } = require('@perkd/errors/dist/app')

const { PASSKEY_NOT_FOUND, VERIFICATION_FAILED, DUPLICATE_CREDENTIAL, BAD_REQUEST, UNAUTHORIZED } = ERRORS,
	{ ACCOUNT_NOT_FOUND } = ACCOUNT_ERR

const isProduction = process.env.NODE_ENV === 'production'

/**
 * WebAuthn/Passkey configuration
 * @typedef {Object} PasskeyConfig
 * @property {Object} webauthn - Core WebAuthn configuration
 *			{string} rpName - Relying Party name displayed to users
 *			{string} origin - Allowed origin for the WebAuthn ceremony
 * 			... see @perkd/passkey for details
 * @property {Number} cleanupInterval - TTL of challenges in ms
 * @property {Object} platforms - Platform-specific configurations
 *			{Object} ios - iOS-specific configuration
 *			{Object} android - Android-specific configuration
 *			{Object} web - Web-specific configuration
 */
const passkeyConfig = {
	webauthn: {
		rpName: 'Perkd',
		origin: 'https://perkd.me',		// will allow any subdomain
		rpIds: ['perkd.me']
	}
}

// Initialize PasskeyServer with configuration
const passkey = new Passkey(passkeyConfig)

// Helper methods for MongoDB Binary conversions
const toMongoBinary = base64url => new Binary(Buffer.from(base64url, 'base64url'))
const fromMongoBinary = binary => binary?.buffer?.toString('base64url')

/**
 * Adds Passkey functionality to a Loopback model
 * @param {Object} Model - The Loopback model to extend
 */
module.exports = function(Model) {

	/**
	 * Initializes passkey registration ceremony
	 * @param {string} userId - User identifier
	 * @param {Request} req - Express request object
	 * @returns {Promise<Object>} Registration options for the client
	 * @throws {Error} If initialization fails
	 */
	Model.registerPasskeyInit = async function(userId, req) {
		try {
			if (!userId) throw errorResponse(BAD_REQUEST, 'User ID is required', BAD_REQUEST)

			const userAgent = req.get('User-Agent'),
				host = req.get('Host').split(':')[0],	// remove port
				origin = req.get('Origin')

	        console.log('[PassKey]registerPasskeyInit', { userAgent, host, origin })

			if (!userAgent || !host || !origin) {
				throw errorResponse(BAD_REQUEST, 'Missing required headers', BAD_REQUEST)
			}

			const account = await Model.findOne({ where: { id: userId } }),
				userName = account?.name

			if (!account) {
				throw errorResponse(ACCOUNT_NOT_FOUND, 'Account not found', BAD_REQUEST)
			}

			return passkey.handleRegistrationRequest(userId, userName, origin, host, userAgent)
		}
		catch (error) {
			// TODO track metrics
			console.error('[Passkey] registerPasskeyInit:', error)
			if (isProduction) {		// mask error details in production
				throw errorResponse(UNAUTHORIZED, '', UNAUTHORIZED)
			}
			else {
				throw error
			}
		}
	}

	/**
	 * Completes passkey registration ceremony
	 * @param {string} userId - User identifier
	 * @param {Object} attestationResponse - Client attestation response
	 * @param {ServerRequest} req - Express request object
	 * @returns {Promise<Object>} Registration result
	 * @throws {Error} If registration completion fails
	 */
	Model.registerPasskeyComplete = async function(userId, attestationResponse, req) {
		try {
			validateOrigin(req, passkey.webauthnConfig)

			const host = req.get('Host').split(':')[0],	// remove port
				origin = req.get('Origin'),
				{ id: credentialID } = attestationResponse,
				account = await Model.findOne({ where: { id: userId } })

			console.log('[PassKey]registerPasskeyComplete', { host, origin })

			if (!account) {
				throw errorResponse(ACCOUNT_NOT_FOUND, 'Account not found', BAD_REQUEST)
			}

			const credentials = await passkey.handleRegistrationVerification(userId, attestationResponse, origin, host)
			await account.registerPasskey(credentials)
			return { verified: true, credentialID }			
		}
		catch (error) {
			// TODO track metrics
			console.error('[Passkey] registerPasskeyComplete:', error)
			if (isProduction) {		// mask error details in production
				throw errorResponse(UNAUTHORIZED, '', UNAUTHORIZED)
			}
			else {
				throw error
			}		
		}
	}

	/**
	 * Initializes passkey authentication ceremony
	 * @param {string} userId - User identifier
	 * @param {Object} req - Express request object
	 * @returns {Promise<Object>} Authentication options for the client
	 * @throws {Error} If initialization fails
	 */
	Model.authenticatePasskeyInit = async function(userId, req) {
		try {
			const userAgent = req.get('User-Agent'),
				host = req.get('Host').split(':')[0],	// remove port
				origin = req.get('Origin'),
				account = await Model.findOne({ where: { id: userId } })

			if (!account) {
				throw errorResponse(ACCOUNT_NOT_FOUND, 'Account not found', BAD_REQUEST)
			}

			return passkey.handleAuthenticationRequest(userId, origin, host, userAgent)
		}
		catch (error) {
			// TODO track metrics
			console.error('[Passkey] authenticatePasskeyInit:', error)
			if (isProduction) {		// mask error details in production
				throw errorResponse(UNAUTHORIZED, '', UNAUTHORIZED)
			}
			else {
				throw error
			}
		}
	}

	/**
	 * Completes passkey authentication ceremony
	 * @param {string} userId - User identifier
	 * @param {Object} assertionResponse - Client assertion response
	 * @param {ServerRequest} req - Express request object
	 * @returns {Promise<Object>} Authentication result with token
	 * @throws {Error} If authentication completion fails
	 */
	Model.authenticatePasskeyComplete = async function(userId, assertionResponse, req) {
		try {
			validateOrigin(req, passkey.webauthnConfig)

			const userAgent = req.get('User-Agent'),
				host = req.get('Host').split(':')[0],	// remove port
				origin = req.get('Origin'),
				{ id } = assertionResponse,
				account = await Model.findOne({ where: { id: userId } })

			if (!account) {
				throw errorResponse(ACCOUNT_NOT_FOUND, 'Account not found', BAD_REQUEST)
			}

			const result = await passkey.handleAuthenticationVerification(userId, assertionResponse, origin, host, userAgent),
				{ verified, authenticatorData } = result,
				{ newCounter } = authenticatorData || {}

			if (!verified) {
				throw errorResponse(VERIFICATION_FAILED, 'Verification failed', BAD_REQUEST)
			}

			// Use verifyPasskey for atomic update and token generation
			return account.verifyPasskey({ id, newCounter })
		}
		catch (error) {
			// TODO track metrics
			console.error('[Passkey] authenticatePasskeyComplete:', error)
			if (isProduction) {		// mask error details in production
				throw errorResponse(UNAUTHORIZED, '', UNAUTHORIZED)
			}
			else {
				throw error
			}
		}
	}

	/**
	 * Lists passkeys for a user (static method)
	 * @param {string} userId - User identifier
	 * @param {Object} req - Express request object
	 * @returns {Promise<Array>} List of passkeys
	 */
	Model.listPasskeys = async function(userId, req) {
		const account = await Model.findOne({ where: { id: userId } })

		return account?.listPasskeys(req)
	}

	/**
	 * Removes a passkey by ID (static method)
	 * @param {string} userId - User identifier
	 * @param {string} credentialId - Base64URL encoded credential ID
	 * @returns {Promise<Object>} Success response
	 */
	Model.removePasskeyById = async function (userId, credentialId) {
		if (!userId) throw errorResponse(BAD_REQUEST, 'User ID is required', BAD_REQUEST)
		if (!credentialId) throw errorResponse(BAD_REQUEST, 'Credential ID is required', BAD_REQUEST)

		const account = await Model.findOne({ where: { id: userId } })

		if (!account) {
			throw errorResponse(ACCOUNT_NOT_FOUND, 'Account not found', BAD_REQUEST)
		}

		await account.removePasskey(credentialId)
		return { success: true }
	}

	/**
	 * Registers a new passkey credential for the account
	 * @param {Object} credential - The credential to register
	 *			{string} id - Base64URL encoded credential ID
	 *			{string} publicKey - Base64URL encoded public key
	 *			{number} [counter=0] - Initial signature counter
	 * @returns {Promise<Model>} The updated model instance
	 * @throws {Error} If registration fails or credential is invalid
	 * @fires passkey_registered
	 */
	Model.prototype.registerPasskey = async function (credential) {
		try {
			validateCredential(credential)

			const { passkeys = [] } = this.toJSON(),
				{ id, publicKey, counter = 0 } = credential,
				isDuplicate = passkeys.some(pk => pk.id === id)

			if (isDuplicate) {
				throw errorResponse(DUPLICATE_CREDENTIAL, 'Passkey already registered', BAD_REQUEST)
			}

			const newPasskey = {
				id: toMongoBinary(id),
				publicKey: toMongoBinary(publicKey),
				counter,
				registeredAt: new Date()
			}

			await this.updateDocument({ $push: { passkeys: newPasskey } })
			this.emitEvent('passkey_registered')
			return this
		}
		catch (error) {
			// TODO track metrics
			console.error('[Passkey] registerPasskey:', error)
			if (isProduction) {		// mask error details in production
				throw errorResponse(UNAUTHORIZED, '', UNAUTHORIZED)
			}
			else {
				throw error
			}
		}
	}

	/**
	 * Verifies a passkey assertion and updates the counter
	 * @param {Object} credential - The credential to verify
	 *			{string} id - Base64URL encoded credential ID
	 *			{number} newCounter - New signature counter value from authenticator
	 * @returns {Promise<Object>} Verification result with auth token
	 * @throws {Error} If verification fails or possible cloned authenticator detected
	 * @fires passkey_authenticated
	 *
	 * Counter Validation:
	 * - Accepts if newCounter >= stored counter (allows for authenticators that don't increment)
	 * - Rejects if newCounter < stored counter (indicates possible cloned authenticator)
	 * - Updates stored counter after successful verification
	 */
	Model.prototype.verifyPasskey = async function (credential) {
		try {
			const { id: accountId, personId, passkeys } = this.toJSON(),
				{ id, newCounter } = credential,
				credentialId = Buffer.from(id, 'base64url').toString(),
				found = passkeys.find(pk => pk.id === credentialId),
				changes = {
				$set: { 'passkeys.$[elem].counter': newCounter }
			},
			arrayFilters = [ { 'elem.id': credentialId } ],
			token = Model.token(String(accountId), personId)

			if (!found) {
				throw errorResponse(PASSKEY_NOT_FOUND, 'Passkey not found', BAD_REQUEST)
			}
			if (newCounter < found.counter) {
				throw errorResponse(VERIFICATION_FAILED, 'Invalid request', BAD_REQUEST)
			}

			await this.updateDocument(changes, { arrayFilters })
			this.emitEvent('passkey_authenticated')

			return { verified: true, token }
		}
		catch (error) {
			// TODO track metrics
			console.error('[Passkey] verifyPasskey:', error)
			if (isProduction) {		// mask error details in production
				throw errorResponse(UNAUTHORIZED, '', UNAUTHORIZED)
			}
			else {
				throw error
			}
		}
	}

	/**
	 * Retrieves all registered passkeys for the account
	 * @returns {Promise<Array<Object>>} Array of registered passkeys
	 */
	Model.prototype.listPasskeys = async function (req) {
		try {
			validateOrigin(req, passkey.webauthnConfig)

			// Get raw MongoDB collection to bypass Loopback's deserialization
			const { dataSource, modelName } = Model,
				collection = dataSource.connector.collection(modelName),
				doc = await collection.findOne({ _id: this.id }),
				{ passkeys = [] } = doc || {}

			if (!doc) {
				throw errorResponse(ACCOUNT_NOT_FOUND, 'Account not found', BAD_REQUEST)
			}

			const result = passkeys
				.filter(key => key && key.id)
				.map(key => ({
					id: fromMongoBinary(key.id),
					publicKey: fromMongoBinary(key.publicKey),
					counter: key.counter || 0,
					registeredAt: key.registeredAt || null
				}))

			return result
		}
		catch (error) {
			// TODO track metrics
			console.error('[Passkey] listPasskeys:', error)
			if (isProduction) {		// mask error details in production
				throw errorResponse(UNAUTHORIZED, '', UNAUTHORIZED)
			}
			else {
				throw error
			}
		}
	}

	/**
	 * Removes a registered passkey from the account
	 * @param {string} id - Base64URL encoded credential ID to remove
	 * @returns {Promise<Model>} The updated model instance
	 * @fires passkey_removed
	 */
	Model.prototype.removePasskey = async function (id) {
		try {
			const changes = {
				$pull: {
					passkeys: { id: toMongoBinary(id) }
				}
			}

			await this.updateDocument(changes)
			this.emitEvent('passkey_removed')
			return this
		}
		catch (error) {
			// TODO track metrics
			console.error('[Passkey] removePasskey:', error)
			if (isProduction) {		// mask error details in production
				throw errorResponse(UNAUTHORIZED, '', UNAUTHORIZED)
			}
			else {
				throw error
			}
		}
	}
}
