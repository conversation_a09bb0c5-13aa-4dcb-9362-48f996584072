/**
 *  @module Mixin:Verification
 */

const { Dates, generateCode } = require('@perkd/utils'),
	{ ACCOUNT: ACCOUNT_ERR } = require('@perkd/errors/dist/app'),
	{ badRequest } = appRequire('lib/common/errorResponse')

const { sameDay } = Dates,
	Settings = appSettings('settings').verificationCode,
	DAILYLIMIT = appSettings('settings').verificationCode.dailyLimit,
	{ ACCOUNT_VCODE_REQUEST_LIMIT, ACCOUNT_MOBILE_CHANGE_LIMIT, ACCOUNT_PASSWORD_REQUEST_LIMIT,
		ACCOUNT_PASSWORD_CHANGE_LIMIT, ACCOUNT_PROMPT_UPGRADE_LIMIT } = ACCOUNT_ERR,
	DEFAULT_LIMIT = 5,
	CHANNEL = {
		sms: 'sms',
		voice: 'voice',
		push: 'push',
		email: 'email',
	}

module.exports = function(Account) {

	Account.PURPOSE = appSettings('settings').verificationCode.purpose

	// -----  Static Methods  -----

	/**
	 * Send password to the account (wrapper method used exclusively by authAssist.js campaign)
	 * @param {String} accountId
	 * @param {String} mobile
	 * @param {String} countryPrefix
	 * @param {Object} locale
	 * @return {Promise}
	 */
	Account.sendPassword = async function(accountId, mobile, countryPrefix, locale) {
		const sendOptions = {
				countryPrefix,
				resend: true,
				locale,
			},
			account = await Account.findById(accountId)

		return account.sendPassword(mobile, new Date(), sendOptions)
	}

	// -----  Instance methods  -----

	Account.prototype._verifyCode = function(code, purpose, mobile) {
		const NOW = new Date()

		return this.verifyCodes.find(vc => (
			(!code || code === vc.code)				// optional
			&& (!purpose || purpose === vc.purpose)		// optional
			&& (!mobile || mobile === vc.mobile)		// optional
			&& (!vc.endTime || NOW <= vc.endTime)
		))
	}

	/**
	 * @param	{String} purpose register, password, changeMobile1
	 * @param	{String} mobile
	 * @param	{String} [code]
	 * @return	{Object} verify
	 */
	Account.prototype.addVerifyCode = async function(purpose, mobile, code) {
		const { name, verifyCodes } = this,
			NOW = new Date(),
			validity = Settings.validitySeconds[purpose],	// in seconds, 0 => no endTime
			verify = {
				code: code || generateCode(Settings.codeLength),
				mobile: mobile || name,
				purpose,
				startTime: NOW,
				createdAt: NOW,
				usedAt: null,
			}

		if (validity > 0) {
			verify.endTime = new Date(NOW.getTime() + (validity * 1000))
		}
		verifyCodes.unshift(verify)	// add to beginning
		await this.updateAttributes({ verifyCodes })

		return verify
	}

	/**
	 * @param	{Object} verifyCode
	 *			{String} code - mandatory, used as pseudo-key
	 *			{String} mobile
	 *			{String} purpose - register, password, changeMobile
	 *			{Date} startTime
	 *			{Date} endTime
	 *			{Date} usedAt
	 *			{Date} createdAt
	 * @return	{Object} verify
	 */
	Account.prototype.updateVerifyCode = async function(verifyCode) {
		const { verifyCodes } = this,
			ndx = verifyCodes.findIndex(vc => (verifyCode.code === vc.code))

		if (ndx === -1) {
			return this.rejectErr('vcode_not_found')
		}
		Object.assign(verifyCodes[ndx], verifyCode)
		await this.updateAttributes({ verifyCodes })

		return verifyCodes[ndx]
	}

	Account.prototype.deleteVerifyCode = async function(code) {
		const { verifyCodes } = this,
			ndx = verifyCodes.findIndex(vc => (code === vc.code))

		if (ndx === -1) {
			return this.rejectErr('vcode_not_found')
		}
		verifyCodes.splice(ndx, 1)
		await this.updateAttributes({ verifyCodes })
	}

	// -----  Sending Codes  -----

	/**
	 * Send verify code to the account
	 * @param	{String}   purpose register, password, changeMobile (see Account.PURPOSE)
	 * @param	{String}   channel sms | whatsapp | email | voice | push
	 * @param	{Object}   options {
	 *                            mobile: '**********',
	 *                            countryPrefix: '65',
	 *                            resend: true,
	 *                            resendTimes: 2,
	 *                            locale: { languages, country, timeZone },
	 *                            operator: 'SingTel',
	 *                            code: '55555',
	 *                            provider: 'clx'
	 *                    }
	 * @return	{Object} { code: code }
	 */
	Account.prototype.sendVerifyCode = async function(purpose, channel, options = {}) {
		const { name } = this,
			mobile = options.mobile || name,
			{ countryPrefix } = options,
			opt = await this.withinDailyLimit(purpose, options) ?? {},		// options mutated!
			useProvider = channel === CHANNEL.sms ? Account.selectProvider(opt.resendTimes, countryPrefix) : undefined

		if (useProvider) opt.useProvider = useProvider

		const { code } = await this.addVerifyCode(purpose, mobile, options.code),
			message = await this.createMessage(purpose, channel, code, opt)

		if (!isProduction()) {
			appNotify(`${purpose}: ${code} (${mobile})`, null, null, '-vcode')
		}

		this.emitEvent('sentVerifyCode', { mobile, countryPrefix, code, purpose, message })
		return { code }
	}

	/**
	 * Send password to the account
	 * @param	{String} 	mobile
	 * @param	{Date}		requestedAt
	 * @param	{Object}   	options
	 *          {String}	countryPrefix - '65'
	 *          {Boolean}	resend - true
	 *          {Object}	locale - { languages, country, timeZone },
	 * @return	{Object}	channel - { channel { sms: true, whatsapp: true, email: true, push: true, voice: true }}
	 */
	Account.prototype.sendPassword = async function(mobile, requestedAt = new Date(), options = {}) {
		const { app } = Account,
			{ channel, countryPrefix } = options,
			{ code } = await this.sendVerifyCode(Account.PURPOSE.password, channel || CHANNEL.sms, options),
			newPassword = code,
			used = { channel: { [ channel || CHANNEL.sms]: true } },
			sendEmail = app.getSettings('emailPassword'),
			sendPush = app.getSettings('pushPassword')

		this.emitEvent('requestedPSWD', { code, requestedAt })

		const [ push, email ] = await Promise.all([
			sendPush?.[countryPrefix] ? this.pushPassword(newPassword, options) : undefined,
			sendEmail?.[countryPrefix] ? this.emailPassword(newPassword, options) : undefined
		])

		if (email) used[CHANNEL.email] = true
		if (push) used[CHANNEL.push] = true

		return { channel: used }
	}

	/**
	 * @param	{String} code
	 * @param	{Object} options
	 *          {String} countryPrefix - '65'
	 *          {Boolean} resend - true
	 *          {Object} locale - { languages, country, timeZone }
	 * @return {Boolean}
	 */
	Account.prototype.emailPassword = async function(code, options) {
		try {
			const PASSWORD = Account.PURPOSE.password,
				person = await this.person.get(),
				{ emailList = [] } = person,
				hasEmail = emailList.length > 0

			if (hasEmail && code && isProduction()) {
				this.createMessage(PASSWORD, CHANNEL.email, code, options)
					.catch(err => false)
				return true
			}
			return false
		}
		catch (err) {
			return false
		}
	}

	/**
	 * @param	{String} code
	 * @param	{Object} options
	 *          {String} countryPrefix - '65'
	 *          {Boolean} resend - true
	 *          {Object} locale - { languages, country, timeZone }
	 * @return {Boolean}
	 */
	Account.prototype.pushPassword = async function(code, options) {
		const { latestDeviceId } = this,
			PASSWORD = Account.PURPOSE.password,
			payload = {
				options: { mute: false, echo: true, sync: false, outside: true }
			},
			success = code && latestDeviceId?.length > 0

		// if (success) self.createMessage(PASSWORD, CHANNEL.push, code, options)
		this.createMessage(PASSWORD, CHANNEL.push, code, Object.assign(options, { payload }))
			.catch(err => appNotify('[AccountPushPassword]', err))
		return success
	}

	// -----  Private Method  -----

	/**
	 * @param	{String} purpose
	 * @param	{Object} options - mutated and used by caller!!!
	 * @return {Promise<Object>} options
	 */
	Account.prototype.withinDailyLimit = async function(purpose, options = {}) {
		const { resend } = options,
			PURPOSE = Account.PURPOSE,
			LIMIT = {
				count: {
					vCodeRequest: 'limits.vCode.requestCount1',
					vCodeRequest2: 'limits.vCode.requestCount2',
					mobileChange: 'limits.mobile.changeCount',
					passwordRequest: 'limits.password.requestCount',
					passwordChange: 'limits.password.changeCount',
					upgrade: 'limits.upgrade.requestCount',
					upgrade86: 'limits.upgrade86.requestCount',
				},
				date: {
					vCodeRequest: 'limits.vCode.lastRequestedAt1',
					vCodeRequest2: 'limits.vCode.lastRequestedAt2',
					mobileChange: 'limits.mobile.lastChangedAt',
					passwordRequest: 'limits.password.lastRequestedAt',
					passwordChange: 'limits.password.lastChangedAt',
					upgrade: 'limits.upgrade.lastRequestedAt',
					upgrade86: 'limits.upgrade86.lastRequestedAt',
				},
				currentLimit: (account, limit) => limit.split('.').reduce((k, v) => k[v] || 0, account),
				// currentLimit gets the account.limits object and return the current count / date
			},
			dailyLimit = DAILYLIMIT[purpose] || DEFAULT_LIMIT

		let countLimit,
			dateLimit

		switch (purpose) {
		case PURPOSE.register:
			if (resend) {
				countLimit = LIMIT.count.vCodeRequest
				dateLimit = LIMIT.date.vCodeRequest
			}
			else return
			break

		case PURPOSE.password:
			if (resend) {
				countLimit = LIMIT.count.passwordRequest
				dateLimit = LIMIT.date.passwordRequest
			}
			else {
				countLimit = LIMIT.count.passwordChange
				dateLimit = LIMIT.date.passwordChange
			}
			break

		case PURPOSE.changeMobile:
			if (resend) {
				countLimit = LIMIT.count.vCodeRequest2
				dateLimit = LIMIT.date.vCodeRequest2
			}
			else {
				countLimit = LIMIT.count.mobileChange
				dateLimit = LIMIT.date.mobileChange
			}
			break

		case PURPOSE.upgrade:
		case PURPOSE.upgrade86:
			countLimit = LIMIT.count[purpose]
			dateLimit = LIMIT.date[purpose]
			break

		default: return
		}

		const NOW = new Date(),
			changes = {},
			count = LIMIT.currentLimit(this, countLimit),
			date = LIMIT.currentLimit(this, dateLimit)

		if (!date || sameDay(NOW, date)) {
			if (count < dailyLimit) {
				changes[countLimit] = count + 1
				changes[dateLimit] = NOW

				await this.updateAttributes(changes)
				// FIXME:
				delete this[countLimit]		// rm key "limits.vCode.requestCount1" - eventstore cannot save
				delete this[dateLimit]		// rm key "limits.vCode.lastRequestedAt1" - eventstore cannot save
			}
			else {
				let err
				switch (purpose) {
				case PURPOSE.register:
					err = badRequest('Maximum number of requests in a day reached', ACCOUNT_VCODE_REQUEST_LIMIT)
					break

				case PURPOSE.password:
					if (resend) {
						err = badRequest('Maximum number of password requests in a day reached', ACCOUNT_PASSWORD_REQUEST_LIMIT)
					}
					else {
						err = badRequest('Maximum number of changes in a day reached', ACCOUNT_PASSWORD_CHANGE_LIMIT)
					}
					break

				case PURPOSE.changeMobile:
					if (resend) {
						err = badRequest('Maximum number of requests in a day reached', ACCOUNT_VCODE_REQUEST_LIMIT)
					}
					else {
						err = badRequest('Maximum number of changes in a day reached', ACCOUNT_MOBILE_CHANGE_LIMIT)
					}
					break
				case PURPOSE.upgrade:
				case PURPOSE.upgrade86:
					err = badRequest('Maximum number of upgrade reminder in a day reached', ACCOUNT_PROMPT_UPGRADE_LIMIT)
					break
				}

				throw err
			}
		}
		else {
			changes[countLimit] = 1
			changes[dateLimit] = NOW

			await this.updateAttributes(changes)
			// FIXME:
			delete this[countLimit]		// rm key "limits.vCode.requestCount1" - eventstore cannot save
			delete this[dateLimit]		// rm key "limits.vCode.lastRequestedAt1" - eventstore cannot save
		}

		// first request password was marked as resend but is not resend, decrease resendTimes
		options.resendTimes = (purpose === PURPOSE.password)
			? changes[countLimit] - 1
			: changes[countLimit]

		return options
	}

	// -----  Remote Methods  -----

	Account.remoteMethod('prototype.addVerifyCode', {
		description: 'Create new verification code',
		http: { path: '/vcode', verb: 'post' },
		accepts: [
			{ arg: 'purpose', type: 'string', required: true, enum: [ 'register', 'password', 'changeMobile' ], description: 'Purpose' },
			{ arg: 'mobile', type: 'string', description: 'default is account\'s mobile' },
			{ arg: 'code', type: 'string', description: 'default to create new code' },
		],
		returns: { type: 'object', root: true },
	})
}
