/**
 *  @module Mixin:UpgradeApi - called by app upgrade script
 */

const semver = require('semver')

module.exports = function(Account) {

	Account.upgrade = async function(personId, from, to) {
		const result = {}

		if (semver.satisfies(from, '4.0.1 - 5.0.x')) {
			const data = await v5_1_0(personId, from)
			Object.assign(result, data)
		}

		return result
	}

	// ----  Upgrade handlers  ----

	async function v5_1_0(personId, from) {
		const { Person } = Account.app.models,
			customerId = await Person.findIdentity(personId, 'stripe', 'customer')

		return { customerId }
	}

	// -----  Remote Methods  -----

	Account.remoteMethod('upgrade', {
		description: 'Upgrade (App API)',
		http: { path: '/app/upgrade', verb: 'get' },
		accepts: [
			{ arg: 'personId', type: 'string', required: true },
			{ arg: 'from', type: 'string', required: true },
			{ arg: 'to', type: 'string', required: true },
		],
		returns: { type: 'object', root: true },
	})
}
