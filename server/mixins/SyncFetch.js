/**
 *  @module Mixin:SyncFetch - for supporting Sync	(used by Sync mixin)
 */
const { settingsFor } = require('@perkd/sync')

module.exports = function(AccountSetting) {

	const { limit: LIMIT } = settingsFor(AccountSetting.name)

	/**
	 * (used by SyncCache mixin)
	 * @param {String} personId
	 * @param {Date} [last]			// used by Sync
	 * @param {String[]} [ids]		// used by Fetch
	 */
	AccountSetting.syncFetch = async function(personId = null, last, ids) {
		const filter = {
			where: { personId },
			limit: LIMIT
		}

		if (ids) {
			filter.where.id = { inq: ids }
		}
		if (last) {
			filter.where.or = [
				{ modifiedAt: { gt: last } },
				{ modifiedAt: null, createdAt: { gt: last } }
			]
		}

		return AccountSetting.find(filter)
	}

	// -----  Remote Methods  -----

	AccountSetting.remoteMethod('syncFetch', {
		description: 'SyncFetch (Sync API)',
		http: { path: '/sync/fetch', verb: 'get' },
		accepts: [
			{ arg: 'personId', type: 'string', required: true },
			{ arg: 'last', type: 'date' },
			{ arg: 'ids', type: [ { type: 'string' } ] },
		],
		returns: { type: [ 'AccountSetting' ], root: true },
	})
}
