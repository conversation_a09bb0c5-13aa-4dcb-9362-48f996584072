/**
 *  @module Mixin:Monitor
 */

// Module Dependencies
const { SlackChannel } = require('@crm/types'),
	{ X_USER_ISSUE } = SlackChannel

module.exports = function(Account) {
	// -----  Instance Methods  -----

	Account.checkNonVerify = function() {
		const filter = {
			order: 'id DESC',
			limit: 5,
		}
		return Account.find(filter).then(accounts => {
			const pass = accounts.find(account => !!account.verifiedAt)
			if (!pass) appNotify('checkNonVerify', { err: { code: 'too_many_non_verify' } }, 'alert', X_USER_ISSUE)
		})
	}
}
