/**
 *  @module Mixin:Notify
 */

const { Err } = require('@perkd/errors')

const CHANNEL = {
		sms: 'sms',
		voice: 'voice',
		push: 'push',
		email: 'email',
	},
	VOICE_PAUSE = ',,'

module.exports = function(Account) {

	/**
	 * Creates a message (wrapper method used exclusively by verify.js campaign)
	 */
	Account.createMessage = async function(accountId, purpose, channel, code, options) {
		const account = await Account.findById(accountId)
		return account.createMessage(purpose, channel, code, options)
	}

	// -----  Private Instance Methods  -----

	/**
	 * Send verification code given a channel
	 * @param	{String}	key purpose - register / password / changeMobile
	 * @param	{String}	channel sms / whatsapp / email / voice / push
	 * @param	{String}	code
	 * @param	{Object}	options
	 *			{String}	countryPrefix
	 *			{String}	mobile
	 *			{Boolean} 	resend - true
	 *			{Number}	resendTimes
	 *			{Object}	locale - { languages, country, timeZone },
	 *			{String[]}	excludeProviders
	 * @return	{Promise}
	 */
	Account.prototype.createMessage = async function(key, channel, code, options = {}) {
		const self = this,
			{ personId } = self,
			{ useProvider, excludeProviders, resendTimes, resend } = options,
			{ Event } = Account.app,
			// contents = appSettings().contents, // canned messages in Account/Content/Messaging?
			eventData = {
				key,
				channel,
				data: { code },
				options: { track: { personId } },
			},
			FN = {
				push: buildPush,
				sms: buildText,
				voice: buildVoice,
				email: buildEmail,
				whatsapp: buildText,
			}

		if (!FN[channel]) {
			const error = {
				code: 'INVALID_CHANNEL',
				message: 'Invalid channel',
				statusCode: 400,
				details: { channel },
			}
			throw new Err(error)
		}

		if (resend && key === Account.PURPOSE.register) {
			key = Account.PURPOSE.resend		// use resend messaging (instead of register)
		}

		if (useProvider) eventData.options.useProvider = useProvider
		if (excludeProviders) eventData.options.excludeProviders = excludeProviders
		if (resendTimes) eventData.options.resendTimes = resendTimes

		const person = (channel === CHANNEL.push)
			? { id: personId }
			: await self.person.get()

		appEmit(Event.account.notify, FN[channel](eventData, person, options))
	}

	// -----  Private Functions  -----

	function buildPush(eventData, person, options) {
		// console.log('>>> buildPush %j', { eventData, person, options });

		eventData.to = { personId: person.id }
		eventData.options.payload = options.payload

		return eventData
	}

	function buildText(eventData, person, options) { // text content: for SMS / Whatsapp
		// console.log('>>> buildSms %j', { eventData, person, options });

		const { locale, phoneList = [] } = person,
			phone = getPhone(phoneList, options)

		eventData.to = phone
		eventData.options.language = locale.languages[0]

		return eventData
	}

	function buildVoice(eventData, person, options) {
		// console.log('>>> buildVoice %j', { eventData, person, options });

		const { locale, phoneList = [] } = person,
			phone = getPhone(phoneList, options)

		eventData.to = phone
		eventData.data.code = eventData.data.code.split('').join(VOICE_PAUSE)
		eventData.options.language = options.locale.languages[0]

		return eventData
	}

	function buildEmail(eventData, person, options) {
		const { familyName, givenName, locale, phoneList = [], emailList = [] } = person,
			phone = getPhone(phoneList, options)

		eventData.to = { email: emailList[0].address }
		eventData.data.person = { familyName, givenName, ...phone }
		eventData.options.language = locale.languages[0]

		return eventData
	}

	function getPhone(phoneList, options) {
		const { countryPrefix, mobile } = options,
			isChangeMobile = countryPrefix && mobile

		return isChangeMobile
			? { countryCode: countryPrefix, number: mobile.substring(countryPrefix.length) }
			: { countryCode: phoneList[0].countryCode, number: phoneList[0].number }
	}

	Account.selectProvider = function(count = 0, countryCode) {
		const { routes } = Account.app.getSettings('sms') || {},
			route = routes[countryCode] || routes.default,
			provider = route[(count - 1) % route.length] || route[0],
			useProvider = Object.keys(provider || {})[0]

		return useProvider
	}

	// -----  Remote & Operation hooks  -----

	Account.remoteMethod('createMessage', {
		description: 'Send a verification code message (only used by verify.js campaign)',
		http: { path: '/createMessage', verb: 'post' },
		accepts: [
			{ arg: 'accountId', type: 'string', required: true },
			{ arg: 'purpose', type: 'string', required: true },
			{ arg: 'channel', type: 'string', required: true },
			{ arg: 'code', type: 'string', required: true },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: 'object', root: true },
	})
}
