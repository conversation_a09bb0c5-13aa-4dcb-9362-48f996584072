/**
 *  @module Mixin:Find (Account)
 */

const { Context } = require('@perkd/multitenant-context'),
	{ ACCOUNT: ACCOUNT_ERR } = require('@perkd/errors/dist/app')

const { ACCOUNT_MOBILE_NOT_VERIFIED, ACCOUNT_DEACTIVATED, ACCOUNT_ID_MISSING, ACCOUNT_NOT_FOUND } = ACCOUNT_ERR

module.exports = function(Account) {

	Account.getStartupCard = async function(personId) {
		if (!personId) return

		const { Card } = Account.app.models,
			filter = {
				where: {
					personId,
					state: 'active',
					'when.registered': null,
					deletedAt: null,
				},
				include: {
					relation: 'cardMaster',
					scope: {
						where: { 'features.managed': true },
						fields: { features: true },
					},
				},
				order: 'createdAt DESC',
			},
			cards = await Card.find(filter),
			[ card ] = cards.filter(card => !!card.cardMaster)

		return card
	}

	// -----  Private Methods  -----

	/**
	 * Get account instance (with person)
	 * @param	{boolean} verified - require verified
	 * @return	{Account}
	 */
	Account.get = async function(verified) {
		console.log('requestContext [3] >>>>>>>>>>>>', Context.getCurrentContext().requestId)
		console.log('------------- 3', Context.accountId)
		const self = this,
			{ accountId } = Context

		if (!accountId) {
			return self.rejectErr(ACCOUNT_ID_MISSING)
		}

		console.log('>>>>>>>>>>>> request: ', Context.getCurrentContext()?.requestId)
		return accountId
		// const filter = {
		// 		include: {
		// 			relation: 'person',
		// 			scope: { fields: [ 'id', 'name', 'phoneList', 'locale' ] },
		// 		}
		// 	},
		// 	account = await Account.findById(accountId, filter)

		// if (!account) {
		// 	return self.rejectErr(ACCOUNT_NOT_FOUND)
		// }
		// if (verified && !account.verifiedAt) {
		// 	return self.rejectErr(ACCOUNT_MOBILE_NOT_VERIFIED)
		// }
		// if (account.deactivatedAt) {
		// 	return self.rejectErr(ACCOUNT_DEACTIVATED)
		// }
		// return account
	}

	/**
	 * Find account(s) by mobile (name)
	 * @param	{string} name
	 * @param	{object} options - conditions
	 * @return	{Account}
	 */
	Account.findByName = async function(name, { verified, notVerified, code, purpose, include, multiple }) {
		const filter = {
			where: {
				name,
				deletedAt: null,
				deactivatedAt: null,
			},
			order: 'createdAt DESC'
		}

		if (!name) {
			return multiple ? [] : undefined
		}
		if (verified) filter.where.verifiedAt = { neq: null }
		if (notVerified) filter.where.verifiedAt = null
		if (code) filter.where['verifyCodes.code'] = code
		if (purpose) filter.where['verifyCodes.purpose'] = purpose
		if (include) filter.include = include

		return multiple ? Account.find(filter) : Account.findOne(filter)
	}
}
