/**
 *  @module Mixin:Token
 */
const Cognito = require('aws-sdk/clients/cognitoidentity'),
	{ Context } = require('@perkd/multitenant-context')

const { aws } = appSettings('settings'),
	{ config, param, retryInterval, refreshInterval } = aws.cognito

const {
	COGNITO_IDENTITY_POOL_ID = param.IdentityPoolId,
	COGNITO_ACCESS_KEY_ID = config.accessKeyId,
	COGNITO_SECRET_ACCESS_KEY = config.secretAccessKey
} = process.env

const COGNITO = {
	config: {
		accessKeyId: COGNITO_ACCESS_KEY_ID,
		secretAccessKey: COGNITO_SECRET_ACCESS_KEY,
		region: 'ap-southeast-1'
	},
	param: {
		IdentityPoolId: COGNITO_IDENTITY_POOL_ID,
		Logins: { perkd: 'account' },
		TokenDuration: 4200
	},
}

const cognito = new Cognito(COGNITO.config),			// TODO: move to "cognito provider"
	RETRY_INTERVAL_MS = retryInterval * 1000,
	REFRESH_INTERVAL_MS = refreshInterval * 1000,
	SUPPORT_DEVICE_BLOCKING = false

module.exports = function(Model) {
	// TODO: refactor with auth service Token.js mixin

	let openId
	refreshOpenId()

	/**
	 * Get Login & Cognito tokens
	 * @param	{Object} opt
	 * 			{Boolean} skipValidate
	 * @return	{Promise<Object>} tokens { token, openId }
	 */
	Model.prototype.getTokens = async function(opt = {}) {
		const { id, personId, options } = this,
			{ forceLogout } = options,
			{ skipValidate } = opt

		try {
			if (!skipValidate) {
				if (forceLogout) throw ''
				await allowInstallation(id)
			}
			return { token: Model.token(id, personId), openId }		// refresh token
		}
		catch (err) {
			return { token: Model.token(id, personId, true) }		// invalidate token
		}
	}

	// -----  Private Functions  -----

	/**
     * Get OpenId Token for CustomCardImage access
     * - 70 mins token
     * - every 10 mins refreshOpenId, app get minimum 60 mins token
     * - retry every 5 secs if refreshOpenId fails
	 * @return {Promise<void>}
     */
	async function refreshOpenId() {
		try {
			/*	getOpenIdTokenForDeveloperIdentity.promise() returns Promise<PromiseResult>,
				returning in then does not render getOpenIdTokenForDeveloperIdentity resolved.
			*/
			const { IdentityId, Token } = await cognito.getOpenIdTokenForDeveloperIdentity(COGNITO.param).promise()

			openId = {
				IdentityId,
				Logins: { 'cognito-identity.amazonaws.com': Token },
			}
			setTimeout(refreshOpenId, REFRESH_INTERVAL_MS)
		}
		catch (error) {
			appLog('[refreshOpenId]', { error })
			setTimeout(refreshOpenId, RETRY_INTERVAL_MS)
		}
	}

	async function allowInstallation(accountId) {
		if (!SUPPORT_DEVICE_BLOCKING) return undefined

		const { app } = Model,
			{ models, Event } = app,
			{ Installation } = models,
			{ installation } = Context,
			ins = await Installation.findById(installation.id)

		if (!ins.isBlocked) return undefined

		const error = new Error('Device blocked')

		app.emit(Event.account.blocked, { accountId, install: installation })
		appLog('accountBlocked', { error })
		throw error
	}
}
