const LOCALHOST = '127.0.0.1',
	{
		NODE_ENV,
		DB_HOST = NODE_ENV ? 'mongodb' : LOCALHOST,
		DB_USERNAME = '',
		DB_PASSWORD = '',
		DB_AUTH = 'admin',
		DB_SET = '',
		SERVICE_HOST = LOCALHOST,
		PERSON_HOST = SERVICE_HOST,
		INSTALLATION_HOST = SERVICE_HOST,
		CARD_MASTER_HOST = SERVICE_HOST,
		CARD_HOST = SERVICE_HOST,
	} = process.env

module.exports = {
	default: {
		name: 'default',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${encodeURIComponent(DB_PASSWORD)}@` : '') + `${DB_HOST}/perkd?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : '') + '&useNewUrlParser=true&useUnifiedTopology=true&maxPoolSize=50&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000',
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true,
	},
	trap: {
		name: 'trap',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${encodeURIComponent(DB_PASSWORD)}@` : '') + `${DB_HOST}/trap?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : '') + '&useNewUrlParser=true&useUnifiedTopology=true&maxPoolSize=50&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000',
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true,
	},
	personRemote: {
		name: 'personRemote',
		connector: 'remote',
		url: `http://${PERSON_HOST}:8101/api`,
	},
	installationRemote: {
		name: 'installationRemote',
		connector: 'remote',
		url: `http://${INSTALLATION_HOST}:8104/api`,
	},
	cardMasterRemote: {
		name: 'cardMasterRemote',
		connector: 'remote',
		url: `http://${CARD_MASTER_HOST}:8116/api`,
	},
	cardRemote: {
		name: 'cardRemote',
		connector: 'remote',
		url: `http://${CARD_HOST}:8117/api`,
	},
}
