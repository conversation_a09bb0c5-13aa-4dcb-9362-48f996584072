/**
 *  @module Model:Account
 */

const { Context } = require('@perkd/multitenant-context'),
	{ Wallet, Messagings, Providers } = require('@crm/types'),
	{ Platform } = require('@perkd/wallet'),
	{ touchpoint } = require('@perkd/touchpoints'),
	{ parsePhoneNumber } = require('@perkd/utils'),
	{ ACCOUNT: ACCOUNT_ERR } = require('@perkd/errors/dist/app'),
	{ Cards } = Wallet,
	{ PERKD } = Providers.PROVIDER,
	{ SMS } = Messagings.Service

const { PERKD_ID } = Cards,
	{ ACCOUNT_MOBILE_INVALID, ACCOUNT_MOBILE_SAME, ACCOUNT_MOBILE_REGISTERED, ACCOUNT_NOT_FOUND } = ACCOUNT_ERR,
	{ ANDROID } = Platform,
	OREO = 8,
	TOKEN_TTL = 30 * 24 * 60 * 60	// 30 days

module.exports = function(Account) {

	// const MESSAGE_TYPES = { 0: 'signup', 1: 'vCode1', 2: 'vCode2', 3: 'password' };
	Account.CONTENTS = appSettings('settings').contents

	// ----- Static Methods -----

	Account.delete = async function() {
		const { app } = Account,
			{ Metric } = app,
			account = await Account.get()

		await account.updateAttributes({ deletedAt: new Date() })
		appMetric(Metric.delete.count, 1)
	}

	Account.issuePerkdID = async function(personId) {
		const { CardMaster } = Account.app.models,
			NOW = new Date(),
			card = {
				preIssued: false,
				barcode: personId,
				number: personId,
				startTime: NOW,
				when: {
					issued: NOW,
					registered: NOW,
				},
			},
			opt = { noPush: true, noNotify: true }

		return CardMaster.issue(PERKD_ID, personId, card, opt)
			.catch(err => {
				appNotify('issuePerkdID', { personId, card, err })
				return null
			})
	}

	// -----  Instance Methods  -----

	/**
     * @param	{String} newMobileNumber
     * @return	{Account}
     */
	Account.prototype.changeMobile = async function(newMobileNumber) {
		const { name: originMobile } = this,
			phoneNumber = parsePhoneNumber(newMobileNumber),
			{ isPossibleMobile, fullNumber } = phoneNumber

		if (!isPossibleMobile) {
			return Account.rejectErr(ACCOUNT_MOBILE_INVALID)
		}
		if (fullNumber === originMobile) {
			return Account.rejectErr(ACCOUNT_MOBILE_SAME)
		}

		// check if the newMobile has already been registered by another user
		await Account.notVerified(fullNumber)
		await this.updateAttributes({ name: fullNumber })
		this.person.get().then(person => person.replacePhone(originMobile, fullNumber))

		return this
	}

	/**
	 * Deactivates account - same mobile can be used to register new account
	 * @return	{Account}
	 */
	Account.prototype.deactivate = async function() {
		const changes = { deactivatedAt: new Date() }

		await this.updateAttributes(changes)
		this.emitEvent('deactivate')
		return this
	}

	/**
	 * Activates account (previously deactivated)
	 * @return	{Account}
	 */
	Account.prototype.activate = async function() {
		const changes = { deactivatedAt: null }

		await this.updateAttributes(changes)
		this.emitEvent('activate')
		return this
	}

	/**
	 * Force logout at next app launch
	 * @param	{Object} options
	 * 			{Object} through
	 * @return	{Account}
	 */
	Account.prototype.forceLogout = async function(options = {}) {
		const { forceLogout } = this.options

		if (forceLogout) return this

		const through = options.through || touchpoint(),
			changes = {
				$set: { 'options.forceLogout': true }
			}

		await this.updateDocument(changes)
		this.emitEvent('forceLogout', { through })
		return this
	}

	/**
	 * Purge Account and all related resources (all data permanently lost)
	 *  - removes Person, cards, offers, etc
	 * @param	{boolean} forced
	 * @return	{Account}
	 */
	Account.prototype.purge = async function(forced = false) {
		// TODO
		return this.rejectErr('purge_not_implemented')
	}

	/**
	 * Reset Account caches
	 *  - delete all Caches and init Cache0
	 * @return	{void}
	 */
	Account.prototype.resetCache = async function() {
		const { personId } = this,
			{ Cache } = Account.app.models

		await Cache.resetCache(personId)
	}

	/**
	 * Find account shortcut
	 * @param	{string} name
	 * @param	{object} options - conditions
	 * @return	{Account}
	 */
	Account.regButNotVerified = async function(name, options = {}) {
		options.notVerified = true

		const account = await Account.findByName(name, options)
		return account || Account.rejectErr(ACCOUNT_NOT_FOUND)
	}

	/**
	 * Find account shortcut
	 * @param	{String} name
	 * @param	{Object} options - conditions
	 * @return	{Account}
	 */
	Account.isVerified = async function(name, options = {}) {
		options.verified = true

		const account = await Account.findByName(name, options)
		return account || Account.rejectErr(ACCOUNT_NOT_FOUND)
	}

	/**
	 * Find account shortcut
	 * @param	{string} name
	 * @param	{object} options - conditions
	 * @return	{Account}
	 */
	Account.notVerified = async function(name, options = {}) {
		options.verified = true

		const account = await Account.findByName(name, options)
		if (account) {
			return Account.rejectErr(ACCOUNT_MOBILE_REGISTERED)
		}
	}

	/**
	 * Generate token
	 * @param {string} accountId
	 * @param {string} personId
	 * @param {boolean} invalidate
	 * @return {object} token
	 */
	Account.token = function(accountId, personId, invalidate = false) {
		const payload = {
			tenant: { code: PERKD },
			user: { accountId, personId },
			exp: invalidate ? 0 : (Math.round(Date.now() / 1000) + TOKEN_TTL),
		}

		return Context.generateAccessToken(payload)
	}

	Account.isSuspicious = function(install = {}, countryCode) {
		const { countries = [] } = Account.app.getSettings('whitelist') || {},
			{ name: osName, version: osVersion } = install?.os || {},
			version = Number(osVersion?.split('.')?.[0]),
			belowAndroid8 = osName === ANDROID && version < OREO		// device id updates on every app install

		return !!((belowAndroid8 && !countries.includes(Number(countryCode))))
	}

	Account.isBlackListed = function(install = {}, countryCode, carrier) {
		const { carriers = [], countries = [], devices = [], installationIds = [] } = Account.app.getSettings('blacklist') || {},
			{ countries: whitelisted = [] } = Account.app.getSettings('whitelist') || {},
			{ id, device = {}, os = {} } = install || {},
			{ model } = device,
			{ name: osName, version: osVersion } = os,
			version = Number(osVersion?.split('.')?.[0]),
			{ name: carrierName } = carrier || {},
			belowAndroid8 = osName === ANDROID && version < OREO

		if (belowAndroid8 && !whitelisted.includes(Number(countryCode))) return true

		if (countries.includes(Number(countryCode)) || carriers.includes(carrierName)) {
			return true
		}

		if (!carrierName) {
			if (Account.isSuspicious(install, countryCode)) {
				return true
			}
			if (!model) return true		// no carrier, no device

			// by device model (when no carrier info)
			if (devices.some(d => d.model === device.model)) {
				return true
			}
		}

		// by installation
		return (id && installationIds.includes(id)) ? true : false
	}

	Account.promptUpgrade = async function(accountId, install, purpose, countryCode) {
		const { upgrade: UPGRADE, upgrade86: UPGRADE86 } = Account.PURPOSE,
			{ minVersion } = Account.app.getSettings('promptUpgrade') || {},
			{ app } = install,
			{ version } = app,
			PATTERN = /\.86$/

		if (version < minVersion) {
			const key = PATTERN.test(version) ? UPGRADE86 : UPGRADE,
				account = await Account.findById(accountId)
			try {
				const options = await account.withinDailyLimit(key, { resend: false }),
					useProvider = Account.selectProvider(options.resendTimes, countryCode)

				if (useProvider) options.useProvider = useProvider
				Account.createMessage(accountId, key, SMS, '', options)
				return undefined
			}
			catch (err) {
				// If daily limit exceeded, skip sending upgrade message
				return undefined
			}
		}
		return purpose
	}

	/**
	 * Emit account related event
	 * @param {string} evtKey
	 * @param {object} data
	 * @param {boolean} includeInstall
	 */
	Account.prototype.emitEvent = function(evtKey, data, includeInstall = true) {
		const { app } = Account,
			{ Event } = app,
			account = this.toJSON(),
			install = includeInstall ? { install: Context.installation } : {},
			body = Object.assign(account, data, install)

		if (Event.account[`${evtKey}`]) {
			app.emit(Event.account[`${evtKey}`], body)
		}
		else {
			appNotify('emitEvent/key_not_match', { evtKey, AllEvents: Event.account }, 'error')
		}
	}
}
