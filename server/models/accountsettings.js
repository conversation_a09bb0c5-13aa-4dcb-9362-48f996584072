/**
 *  @module Model:AccountSetting
 */

const { parsePhoneNumber } = require('@perkd/utils'),
	{ byCountryCode } = require('@perkd/wallet-regions')

module.exports = function(AccountSetting) {

	AccountSetting.init = async function(account = {}) {
		const { id: accountId, name, personId } = account,
			phoneNumber = parsePhoneNumber(name),
			{ regionCode } = phoneNumber,
			settings = {
				discover: {
					regions: byCountryCode(regionCode),
				},
				accountId,
				personId,
				// card.displayName ---- at this stage, there's no display name
			}

		return AccountSetting.create(settings)
	}
}
