{"name": "Settings", "plural": "Settings", "description": "", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {}, "mixins": {"Timestamp": true, "Event": {"hook": {"create": true, "update": true}}, "CacheEvents": {"service": "xaccount"}, "Sync": true}, "properties": {"appId": {"type": "string"}, "version": {"type": {"major": {"type": "number", "default": 0}, "minor": {"type": "number", "default": 0}}, "required": true}, "kind": {"type": "string"}, "data": {"type": "object", "default": {}}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "hidden": [], "validations": [], "relations": {}, "acls": [], "indexes": {}, "methods": {}}