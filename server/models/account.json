{"name": "Account", "plural": "Accounts", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"Timestamp": true, "Errors": true, "Common": true, "Mongo": true, "Event": {"hook": {"update": true, "delete": true}}, "Token": true, "Queue": true, "Find": true, "Notify": true, "Auth": true, "Verification": true, "Passkey": true, "AppApi": true, "UpgradeApi": true, "Monitor": true, "DisableAllRemotes": {"create": true, "upsert": true, "updateAll": false, "patchOrCreate": true, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "findOrCreate": true, "createChangeStream": false, "deleteById": true, "confirm": false, "count": true, "exists": true, "replaceById": false, "replaceOrCreate": false, "upsertWithWhere": false, "prototype.__get__preference": true}}, "options": {"validateUpsert": true}, "properties": {"name": {"type": "String", "required": true}, "password": {"type": "String"}, "options": {"type": {"forceLogout": {"type": "Boolean", "default": false, "description": "Force logout at next app launch"}}, "default": {}}, "limits": {"type": {"password": {"type": {"requestCount": {"type": "Number", "default": 0}, "lastRequestedAt": {"type": "Date"}, "changeCount": {"type": "Number", "default": 0}, "lastChangedAt": {"type": "Date"}}}, "vCode": {"type": {"requestCount1": {"type": "Number", "default": 0}, "lastRequestedAt1": {"type": "Date"}, "requestCount2": {"type": "Number", "default": 0}, "lastRequestedAt2": {"type": "Date"}}}, "mobile": {"type": {"changeCount": {"type": "Number", "default": 0}, "lastChangedAt": {"type": "Date"}}}}, "default": {}}, "verifyCodes": {"type": [{"type": {"code": {"type": "String", "description": "Unique verification code"}, "mobile": {"type": "String", "description": "Mobile number"}, "purpose": {"type": "string", "enum": ["register", "password", "changeMobile"]}, "startTime": {"type": "Date"}, "endTime": {"type": "Date"}, "usedAt": {"type": "Date"}, "createdAt": {"type": "Date"}}}], "default": []}, "registeredAt": {"type": "Date", "default": null, "description": "The date and time of sign up"}, "verifiedAt": {"type": "date", "default": null, "description": "The date and time account verified"}, "lastLoggedInAt": {"type": "Date", "default": null, "description": "The date and time account last logged in"}, "deactivatedAt": {"type": "Date", "default": null, "description": "The date and time account was deactivated"}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}, "deletedAt": {"type": "Date", "default": null}, "passkeys": {"type": [{"type": {"id": {"type": "string", "description": "Base64URL-encoded credential ID"}, "publicKey": {"type": "string", "description": "Base64URL-encoded public key for verifying assertions"}, "counter": {"type": "number", "description": "Signature counter to prevent replay attacks", "minimum": 0}, "registeredAt": {"type": "date", "description": "When the passkey was registered"}}}], "default": [], "description": "Registered passkeys for this account"}}, "hidden": ["password", "verifyCodes", "options"], "validations": [], "relations": {"person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}, "installations": {"type": "hasMany", "model": "Installation", "foreignKey": "accountId"}, "accountSetting": {"type": "hasOne", "model": "AccountSetting", "foreignKey": "accountId"}}, "acls": [], "indexes": {"name_index": {"keys": {"name": 1}}, "personId_index": {"keys": {"personId": 1}}, "registeredAt_index": {"keys": {"registeredAt": 1}}, "verifiedAt_index": {"keys": {"verifiedAt": 1}}, "deactivatedAt_index": {"keys": {"deactivatedAt": 1}}, "deletedAt_index": {"keys": {"deletedAt": 1}}, "passkeys_id_index": {"keys": {"passkeys.id": 1}, "options": {"unique": true, "sparse": true}}}, "scopes": {}, "methods": {"delete": {"description": "Delete (soft) Account", "http": {"path": "/delete", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "register": {"description": "Register new Account (App API)", "http": {"path": "/app/register", "verb": "post"}, "accepts": [{"arg": "profile", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"description": "Access Token (JWT)", "type": "object", "root": true}}, "verify": {"description": "Verify account (App API)", "http": {"path": "/app/verify", "verb": "post"}, "accepts": [{"arg": "code", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "requestVerifyCode": {"description": "Request resend of verification code (App API)", "http": {"path": "/app/verify", "verb": "get"}, "accepts": [{"arg": "mobile", "type": "string", "required": true}, {"arg": "channel", "type": "string"}, {"arg": "purpose", "type": "string"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "launch": {"description": "Launch app (App API)", "http": {"path": "/app/launch", "verb": "post"}, "accepts": [{"arg": "installation", "type": "object", "required": true}, {"arg": "at", "type": "date"}], "returns": {"type": "object", "root": true}}, "login": {"description": "Login (App API)", "http": {"path": "/app/login", "verb": "post"}, "accepts": [{"arg": "mobile", "type": "string"}, {"arg": "password", "type": "string"}, {"arg": "touchID", "type": "boolean", "default": false}, {"arg": "accountId", "type": "string", "default": false}], "returns": {"type": "object", "root": true}}, "refreshToken": {"description": "Refresh JWT token (App API)", "http": {"path": "/app/token/refresh", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "logout": {"description": "Logout (App API)", "http": {"path": "/app/logout", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "requestPassword": {"description": "Request password (App API)", "http": {"path": "/app/password", "verb": "get"}, "accepts": [{"arg": "mobile", "type": "string", "required": true}, {"arg": "channel", "type": "string"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "changePassword": {"description": "Change password (App API)", "http": {"path": "/app/password", "verb": "post"}, "accepts": [{"arg": "newPassword", "type": "string", "required": true}, {"arg": "oldPassword", "type": "string"}, {"arg": "ignoreOld", "type": "boolean"}], "returns": {"type": "object", "root": true}}, "sendPassword": {"description": "Send password to the account (only used by authAssist.js campaign)", "http": {"path": "/sendPassword", "verb": "post"}, "accepts": [{"arg": "accountId", "type": "string", "required": true}, {"arg": "mobile", "type": "string", "required": true}, {"arg": "countryPrefix", "type": "string", "required": true}, {"arg": "locale", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "changeMobile": {"description": "Change account mobile number (App API)", "http": {"path": "/app/mobile", "verb": "post"}, "accepts": [{"arg": "mobile", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "verifyChangeMobile": {"description": "Verify mobile number change (App API)", "http": {"path": "/app/mobile/verify", "verb": "post"}, "accepts": [{"arg": "mobile", "type": "string", "required": true}, {"arg": "code", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "registerPasskeyInit": {"description": "Initialize passkey registration ceremony", "http": {"path": "/app/auth/register-passkey", "verb": "post"}, "accepts": [{"arg": "userId", "type": "string", "required": true, "description": "User identifier"}, {"arg": "req", "type": "object", "http": {"source": "req"}, "description": "Express request object"}], "returns": {"arg": "data", "type": "object", "root": true, "description": "Registration options for WebAuthn ceremony"}}, "registerPasskeyComplete": {"description": "Complete passkey registration ceremony", "http": {"path": "/app/auth/register-passkey/verify", "verb": "post"}, "accepts": [{"arg": "userId", "type": "string", "required": true, "description": "User identifier"}, {"arg": "attestationResponse", "type": "object", "required": true, "description": "Client attestation response"}, {"arg": "req", "type": "object", "http": {"source": "req"}, "description": "Express request object"}], "returns": {"arg": "data", "type": "object", "root": true, "description": "Registration result with credential ID"}}, "authenticatePasskeyInit": {"description": "Initialize passkey authentication ceremony", "http": {"path": "/app/auth/authenticate-passkey", "verb": "post"}, "accepts": [{"arg": "userId", "type": "string", "required": true, "description": "User identifier"}, {"arg": "req", "type": "object", "http": {"source": "req"}, "description": "Express request object"}], "returns": {"arg": "data", "type": "object", "root": true, "description": "Authentication options for WebAuthn ceremony"}}, "authenticatePasskeyComplete": {"description": "Complete passkey authentication ceremony", "http": {"path": "/app/auth/authenticate-passkey/verify", "verb": "post"}, "accepts": [{"arg": "userId", "type": "string", "required": true, "description": "User identifier"}, {"arg": "assertionResponse", "type": "object", "required": true, "description": "Client assertion response"}, {"arg": "req", "type": "object", "http": {"source": "req"}, "description": "Express request object"}], "returns": {"arg": "data", "type": "object", "root": true, "description": "Authentication result with token"}}, "listPasskeys": {"description": "Get registered passkeys for a user", "http": {"path": "/app/auth/list-passkeys", "verb": "get"}, "accepts": [{"arg": "userId", "type": "string", "http": {"source": "query"}, "required": true}, {"arg": "req", "type": "object", "http": {"source": "req"}, "description": "Express request object"}], "returns": {"type": "array", "root": true, "description": "Array of registered passkeys"}}, "removePasskeyById": {"description": "Remove a registered passkey", "http": {"path": "/app/auth/remove-passkey", "verb": "delete"}, "accepts": [{"arg": "userId", "type": "string", "required": true, "description": "User identifier"}, {"arg": "credentialId", "type": "string", "required": true, "description": "Base64URL encoded credential ID"}, {"arg": "req", "type": "object", "http": {"source": "req"}, "description": "Express request object"}], "returns": {"arg": "response", "type": "object", "root": true, "description": "Removal operation result"}}, "prototype.changeMobile": {"description": "Change account mobile number", "http": {"path": "/mobile", "verb": "post"}, "accepts": [{"arg": "mobile", "type": "string", "required": true}], "returns": {"type": "Account", "root": true}}, "prototype.deactivate": {"description": "Deactivate account", "http": {"path": "/deactivate", "verb": "post"}, "returns": {"type": "Account", "root": true}}, "prototype.activate": {"description": "Activate account", "http": {"path": "/activate", "verb": "post"}, "returns": {"type": "Account", "root": true}}, "prototype.forceLogout": {"description": "Force logout at next app launch", "http": {"path": "/logout", "verb": "post"}, "accepts": [{"arg": "options", "type": "object", "description": "{ through }"}], "returns": {"type": "Account", "root": true}}, "prototype.resetCache": {"description": "Delete all caches and init cache0", "http": {"path": "/resetCache", "verb": "post"}, "returns": {"type": "object", "root": true}}}}