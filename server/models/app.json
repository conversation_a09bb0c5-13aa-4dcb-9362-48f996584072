{"name": "App", "plural": "Apps", "description": "", "base": "Application", "idInjection": true, "strict": false, "options": {}, "mixins": {"Timestamp": true}, "properties": {"appID": {"type": "string"}, "version": {"type": {"major": {"type": "number", "default": 0}, "minor": {"type": "number", "default": 0}}, "required": true}, "settings": {"type": "object", "default": {}}, "credentials": {"type": {"encryption": {"type": "string"}, "signature": {"type": "string"}}, "default": {}, "description": "{ key: 'credential' } pairs for storing external credentials, 'encryption' & 'signature' are reserved"}, "cardMasterBundledUntil": {"type": "date"}, "releasedAt": {"type": "date"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "hidden": [], "validations": [], "relations": {}, "acls": [], "indexes": {"appID_index": {"keys": {"appID": 1}}, "version_index": {"keys": {"version": 1}}}, "methods": {}}