/**
 *  @module Model:App
 */

const { getAppFilter } = require('@crm/loopback').App

module.exports = function(App) {
	App._model = 'App'

	/**
	 * @param	{Object} 				params
	 * 			{Object} 				params.install
	 * 			{Number} 				params.lastSync - Model lastSync timestamp
	 * 			{Object} 				params.options
	 * @return	{Promise<Object>}		filter for syncFetch
	 */
	App.syncFilter = async function(params) {
		const { install, lastSync = 0, syncUntil = 0, options } = params,
			filter = getAppFilter(install.app),
			from = options.syncUntil !== undefined ? options.syncUntil : options.lastSync !== undefined ? options.lastSync
				: syncUntil !== undefined ? syncUntil : lastSync

		if (!options.reset && from) {
			const fromDate = new Date(from)

			filter.where.or = [
				{ modifiedAt: { gt: fromDate } },
				{ modifiedAt: null, createdAt: { gt: fromDate } },
			]
		}

		filter.limit = options.fetchLimit

		return filter
	}

	/**
     * @param	{Object} app
	 * 			{String} version
	 * 			{String} id
     * @return	{App}
     */
	App.get = async function(app) {
		const filter = getAppFilter(app)
		return App.findOne(filter)
	}

	// -----  Remote & Operation hooks  -----

	// App.disableRemoteMethodByName('create');
	// App.disableRemoteMethodByName('upsert');
	App.disableRemoteMethodByName('updateAll')

	// App.disableRemoteMethodByName('patchOrCreate'); // PATCH /Model/
	// App.disableRemoteMethodByName('prototype.patchAttributes'); // PATCH /Model/{id}

	// App.disableRemoteMethodByName('find');
	// App.disableRemoteMethodByName('findById');
	// App.disableRemoteMethodByName('findOne');

	App.disableRemoteMethodByName('createChangeStream')

	// App.disableRemoteMethodByName('deleteById');

	App.disableRemoteMethodByName('confirm')
	// App.disableRemoteMethodByName('count');
	// App.disableRemoteMethodByName('exists');

	App.disableRemoteMethodByName('replaceById') // POST /Model/{id}/replace
	App.disableRemoteMethodByName('replaceOrCreate') // PUT  /Model
	// POST /Model/replaceOrCreate
	App.disableRemoteMethodByName('upsertWithWhere')
}
