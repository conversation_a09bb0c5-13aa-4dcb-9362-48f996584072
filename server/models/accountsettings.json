{"name": "AccountSetting", "plural": "AccountSettings", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"Timestamp": true, "Errors": true, "Mongo": true, "Event": {"hook": {"create": true, "update": true}}, "SyncFetch": true, "CacheEvents": {"service": "xaccount"}, "DisableAllRemotes": {"create": true, "upsert": true, "updateAll": false, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "findOrCreate": true, "createChangeStream": false, "deleteById": false, "confirm": false, "count": false, "exists": false, "replaceById": false, "replaceOrCreate": true, "upsertWithWhere": true}}, "options": {"validateUpsert": true}, "properties": {"reminders": {"type": {"card": {"type": {"kind": {"type": "string", "enum": ["expiry"]}, "days": {"type": "number"}, "timeOfDay": {"type": "number"}}}, "offer": {"type": {"kind": {"type": "string", "enum": ["expiry"]}, "days": {"type": "number"}, "timeOfDay": {"type": "number"}}}}, "required": true, "default": {"card": {"kind": "expiry", "days": 14, "timeOfDay": ********}, "offer": {"kind": "expiry", "days": 7, "timeOfDay": ********}}}, "discover": {"type": {"cards": {"type": "boolean", "default": true}, "regions": {"type": "array", "description": "Cards available in these regions will be listed in Discover automatically"}}, "default": {}}, "settings": {"type": "object", "default": {}, "description": "settings:{app:{features:{shop:{defaultTab:true}}}}"}, "bioAuth": {"type": "boolean", "default": true}, "scan": {"type": "boolean", "default": true}, "logs": {"type": {"severity": {"type": "number", "default": 2, "description": "Threshold to sync 'log' AppEvents. Default to WARNING or more severe. { DEBUG: 1, INFO: 2, WARNING: 3, ERROR: 4, CRITICAL: 5 }"}}, "default": {}}, "card": {"type": {"view": {"type": "string", "enum": ["big", "small"], "default": "big", "description": "Size of Card face in Card List"}, "order": {"type": "string", "enum": ["brand", "location", "custom"], "default": "custom"}, "displayName": {"type": "string"}, "views": {"type": "object", "default": {}, "description": "Android custom sort optimization based on card views, used by App version 4.0 and below"}, "sort": {"type": ["string"], "default": []}}, "default": {}}, "payments": {"type": "object", "default": {}, "description": "Placeholder for payment preferences"}, "social": {"type": "object", "default": {}, "description": "Placeholder for social media platform preferences"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date", "default": null}}, "validations": [], "relations": {"account": {"type": "belongsTo", "model": "Account", "foreignKey": "accountId"}, "person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}}, "acls": [], "indexes": {"accountId_index": {"keys": {"accountId": 1}}}, "scopes": {}, "methods": {}}