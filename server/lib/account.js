/**
 *  @module Service:Account
 */

//  Module Dependencies
const Service = appRequire('lib/common/Service')

class AccountService extends Service {
	_ready() {
		const self = this,
			{ app } = self,
			{ Event, models } = app,
			{ Account } = models

		// ----  Event Listeners  ----
		// Handle Person Mobile update
		app.on(Event.person.person.updated, changeMobile)
		// Handle offline app launch
		app.on(Event.sync.app.app.launch.offline, emitLaunchEvent)

		// ----  Event Handlers  ----
		function changeMobile({ data }) {
			const phoneUpdate = (data.context.delta || []).find(delta => delta.path === 'phoneList')

			if (phoneUpdate && data.phoneList && data.phoneList[0]) {
				const newPhoneNumber = data.phoneList[0].fullNumber

				Account.findOne({ where: { personId: data.id } })
					.then(account => {
						if (account && (account.name !== newPhoneNumber)) {
							return account.updateAttributes({ name: newPhoneNumber })
						}
					})
					.catch(err =>{
						console.log('[changeMobile]', { err, data })
					})
			}
		}

		function emitLaunchEvent({ data }) {
			const { personId, install, location, occurredAt: launchedAt, context } = data
			appEmit(app.Event.account.launched, { personId, install, location, launchedAt, context })
		}
	}
}

module.exports = exports = AccountService

/**
 * End of script
 */
