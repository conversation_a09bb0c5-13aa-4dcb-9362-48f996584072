/**
 *  @module Install	middleware for calls initiated by wallet app
 *
 *  Setup perkd-install in LB Context from request Header
 */

const { Context } = require('@perkd/multitenant-context'),
	{ languageOf } = require('@perkd/utils'),
	{ installation } = require('@perkd/wallet')

module.exports = function() {

	return function injectInstall(req, res, next) {
		console.log('[MIDDLEWARE-TRACE] INSTALL middleware - requestContext [3] >>>>>>>>>>>>', Context.getCurrentContext()?.requestId)
		console.log('[MIDDLEWARE-TRACE] INSTALL middleware - Context.accountId:', Context.accountId)
		console.log('[MIDDLEWARE-TRACE] INSTALL middleware - Context exists:', !!Context.getCurrentContext())
		console.log('[MIDDLEWARE-TRACE] INSTALL middleware - Context keys:', Context.getCurrentContext() ? Object.keys(Context.getCurrentContext()) : 'null')
		// const install = installation(req.headers),
		// 	{ locale = {} } = install ?? {},
		// 	language = languageOf(locale)

		// if (install) {
		// 	Context.installation = install
		// 	Context.language = language
		// }

		next()
	}
}
