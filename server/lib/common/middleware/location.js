/**
 *  @module location	middleware for calls initiated by wallet app
 *
 *  Setup perkd-location in LB Context from request Header
 */

const { Context } = require('@perkd/multitenant-context'),
	{ location } = require('@perkd/wallet')

module.exports = function() {

	return function injectLocation(req, res, next) {
		console.log('[MIDDLEWARE-TRACE] LOCATION middleware - requestContext [3] >>>>>>>>>>>>', Context.getCurrentContext()?.requestId)
		console.log('[MIDDLEWARE-TRACE] LOCATION middleware - Context.accountId:', Context.accountId)
		console.log('[MIDDLEWARE-TRACE] LOCATION middleware - Context exists:', !!Context.getCurrentContext())
		console.log('[MIDDLEWARE-TRACE] LOCATION middleware - Context keys:', Context.getCurrentContext() ? Object.keys(Context.getCurrentContext()) : 'null')
		// const payload = location(req.headers)

		// if (payload) {
		// 	Context.location = payload
		// }

		next()
	}
}
