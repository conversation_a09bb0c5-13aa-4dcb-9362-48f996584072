/**
 * Context Cleanup Middleware
 *
 * Dedicated middleware for handling context lifecycle cleanup.
 * Should be registered LAST in the middleware chain to ensure
 * cleanup happens after all request processing is complete.
 */

const Context = require('@perkd/multitenant-context')
const debug = require('debug')('middleware:context-cleanup')

module.exports = function contextCleanupMiddleware(options = {}) {
	console.log('***************1')
	const {
		timeout = 300000, // 5min fallback timeout
		enableLogging = true
	} = options

	return function contextCleanup(req, res, next) {
		console.log('***************2')
		const requestId = req.requestId || 'unknown'
		const startTime = Date.now()

		// Get current context to clean up later
		const currentContext = Context.getCurrentContext()

		if (!currentContext) {
			debug(`[${requestId}] No context to clean up`)
			return next()
		}

		debug(`[${requestId}] Context cleanup middleware initialized`)

		// Track if cleanup has been done to prevent double cleanup
		let cleanupDone = false

		const performCleanup = (reason = 'unknown') => {
			console.log('***************3')
			if (cleanupDone) {
				debug(`[${requestId}] Cleanup already performed, skipping`)
				return
			}

			cleanupDone = true
			const duration = Date.now() - startTime

			try {
				// Release the context
				Context.releaseContext(currentContext)

				if (enableLogging) {
					debug(`[${requestId}] Context cleaned up (reason: ${reason}, duration: ${duration}ms)`)
				}

				// Clear timeout if it exists
				if (timeoutId) {
					clearTimeout(timeoutId)
				}

			}
			catch (error) {
				debug(`[${requestId}] Error during context cleanup:`, error)
			}
		}

		// Primary cleanup: When response finishes
		res.on('finish', () => performCleanup('response-finish'))
		res.on('close', () => performCleanup('response-close'))
		res.on('error', () => performCleanup('response-error'))

		// Secondary cleanup: When request is aborted
		req.on('aborted', () => performCleanup('request-aborted'))
		req.on('close', () => performCleanup('request-close'))

		// Fallback cleanup: Timeout-based (safety net)
		const timeoutId = setTimeout(() => {
			debug(`[${requestId}] Context cleanup timeout triggered after ${timeout}ms`)
			performCleanup('timeout')
		}, timeout)

		// Continue to next middleware
		next()
	}
}

/**
 * Express error handler that ensures context cleanup even on errors
 */
function contextCleanupErrorHandler() {
	return function cleanupOnError(err, req, res, next) {
		const requestId = req.requestId || 'unknown'
		const currentContext = Context.getCurrentContext()

		if (currentContext) {
			debug(`[${requestId}] Cleaning up context due to error: ${err.message}`)
			try {
				Context.releaseContext(currentContext)
			}
			catch (cleanupError) {
				debug(`[${requestId}] Error during error-triggered cleanup:`, cleanupError)
			}
		}

		next(err)
	}
}

module.exports.contextCleanupErrorHandler = contextCleanupErrorHandler
