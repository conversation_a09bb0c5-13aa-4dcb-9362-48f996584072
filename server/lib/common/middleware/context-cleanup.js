/**
 * Context Cleanup Middleware
 *
 * Dedicated middleware for handling context lifecycle cleanup.
 * Should be registered LAST in the middleware chain to ensure
 * cleanup happens after all request processing is complete.
 */

const { Context } = require('@perkd/multitenant-context')
const debug = require('debug')('middleware:context-cleanup')

module.exports = function contextCleanupMiddleware(options = {}) {
	console.log('***************1 - Context cleanup middleware factory called')
	const {
		timeout = 300000, // 5min fallback timeout
		enableLogging = true
	} = options

	console.log('***************1.5 - Returning middleware function')
	return function contextCleanup(req, res, next) {
		console.log('***************2 - Context cleanup middleware CALLED for:', req.method, req.url)
		console.log('***************2.1 - originalUrl:', req.originalUrl)
		console.log('***************2.2 - baseUrl:', req.baseUrl)
		console.log('***************2.3 - path:', req.path)
		const requestId = req.requestId || 'unknown'
		const startTime = Date.now()

		// Get current context to clean up later
		const currentContext = Context.getCurrentContext()

		if (!currentContext) {
			console.log(`***************2.5 - No context to clean up for request: ${requestId}`)
			debug(`[${requestId}] No context to clean up`)
			return next()
		}

		console.log(`***************2.7 - Context found for cleanup, requestId: ${requestId}`)
		debug(`[${requestId}] Context cleanup middleware initialized`)

		// Track if cleanup has been done to prevent double cleanup
		let cleanupDone = false

		const performCleanup = (reason = 'unknown') => {
			console.log('***************3 - Performing cleanup, reason:', reason)
			if (cleanupDone) {
				debug(`[${requestId}] Cleanup already performed, skipping`)
				return
			}

			cleanupDone = true
			const duration = Date.now() - startTime

			try {
				// Release the context
				Context.releaseContext(currentContext)

				if (enableLogging) {
					debug(`[${requestId}] Context cleaned up (reason: ${reason}, duration: ${duration}ms)`)
				}

				// Clear timeout if it exists
				if (timeoutId) {
					clearTimeout(timeoutId)
				}

			}
			catch (error) {
				debug(`[${requestId}] Error during context cleanup:`, error)
			}
		}

		// Primary cleanup: When response finishes
		res.on('finish', () => performCleanup('response-finish'))
		res.on('close', () => performCleanup('response-close'))
		res.on('error', () => performCleanup('response-error'))

		// Secondary cleanup: When request is aborted
		req.on('aborted', () => performCleanup('request-aborted'))
		req.on('close', () => performCleanup('request-close'))

		// Fallback cleanup: Timeout-based (safety net)
		const timeoutId = setTimeout(() => {
			debug(`[${requestId}] Context cleanup timeout triggered after ${timeout}ms`)
			performCleanup('timeout')
		}, timeout)

		console.log('***************2.9 - Context cleanup middleware setup complete, calling next()')
		// Continue to next middleware
		next()
	}
}

/**
 * Express error handler that ensures context cleanup even on errors
 */
function contextCleanupErrorHandler() {
	return function cleanupOnError(err, req, res, next) {
		const requestId = req.requestId || 'unknown'
		const currentContext = Context.getCurrentContext()

		if (currentContext) {
			debug(`[${requestId}] Cleaning up context due to error: ${err.message}`)
			try {
				Context.releaseContext(currentContext)
			}
			catch (cleanupError) {
				debug(`[${requestId}] Error during error-triggered cleanup:`, cleanupError)
			}
		}

		next(err)
	}
}

module.exports.contextCleanupErrorHandler = contextCleanupErrorHandler
