/**
 *  @module error-handler	middleware
 *
 *  Customized error handler, handle errors before loopback default error handler
 *  Also handles context cleanup to prevent memory leaks
 *
 */

const { Context } = require('@perkd/multitenant-context')
const debug = require('debug')('middleware:error-handler')

// statusCode: [code]
const FILTER_LIST = {
		404: [ 'MODEL_NOT_FOUND' ]
	},
	NOT_FOUND = 'convertNullToNotFoundError'

module.exports = function() {
	return function errorHandler(err, req, res, next) {
		const { statusCode, code, stack } = err,
			{ originalUrl, headers, body, params, query } = req

		const requestId = req.requestId || 'unknown'

		// Clean up context on error to prevent memory leaks
		const currentContext = Context.getCurrentContext()
		if (currentContext) {
			debug(`[${requestId}] Cleaning up context due to error: ${err.message}`)
			try {
				Context.releaseContext(currentContext)
			}
			catch (cleanupError) {
				debug(`[${requestId}] Error during context cleanup:`, cleanupError)
			}
		}

		if (FILTER_LIST[statusCode] && FILTER_LIST[statusCode].includes(code)) {
			if (stack.includes(NOT_FOUND)) {
				res.status(200)
				res.type('application/json')
				return res.send('null')
			}
		}
		err.originalUrl = originalUrl
		next(err)
	}
}
