{"_meta": {"sources": ["loopback/common/models", "loopback/server/models", "../common/models", "./lib/common/models", "./lib/perkd/models", "./models"], "mixins": ["loopback/common/mixins", "loopback/server/mixins", "../common/mixins", "./lib/common/mixins", "./lib/perkd/mixins", "./mixins"]}, "User": {"dataSource": "db", "public": false}, "AccessToken": {"dataSource": "db", "public": false}, "ACL": {"dataSource": "db", "public": false}, "RoleMapping": {"dataSource": "db", "public": false}, "Role": {"dataSource": "db", "public": false}, "Service": {"dataSource": "db", "public": true}, "Account": {"dataSource": "default", "public": true}, "App": {"dataSource": "default", "public": true}, "Settings": {"dataSource": "default", "public": true}, "Locale": {"dataSource": "transient", "public": false}, "Geometry": {"dataSource": "transient", "public": false}, "Permission": {"dataSource": "transient", "public": false}, "AccountSetting": {"dataSource": "default", "public": true}, "Person": {"dataSource": "person<PERSON><PERSON><PERSON>", "public": false}, "Installation": {"dataSource": "installationRemote", "public": false}, "CardMaster": {"dataSource": "cardMasterRemote", "public": false}, "Card": {"dataSource": "cardRemote", "public": false}}