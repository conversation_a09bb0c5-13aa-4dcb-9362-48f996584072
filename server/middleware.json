{"initial": {"./lib/common/middleware/stats": {"name": "stats", "paths": ["/api/Accounts"], "params": {"*": "request", "/app/launch": "login.auto"}, "enabled": true}, "compression": {}, "loopback#favicon": {}, "cors": {"params": {"origin": true, "credentials": true, "maxAge": 86400}}}, "session": {}, "auth": {}, "auth:after": {"./middleware/multitenant-refresh": {"name": "multitenant-refresh", "paths": ["/api/Accounts/app/login", "/api/Accounts/app/token/refresh", "/api/Accounts/app/logout", "/api/Accounts/app/upgrade"]}, "./lib/common/middleware/multitenant": {"name": "multitenant", "paths": ["/api/Accounts(/|$)(?!*app/(login|logout|token/refresh|upgrade)$)*", "/api/AccountSettings", "/api/Apps", "/api/Settings"], "params": {"tenant-code": "trap"}, "enabled": true}, "./lib/common/middleware/install": {"name": "install", "paths": ["/api/Accounts/app", "/api/Apps/app", "/api/Settings/app"]}, "./lib/common/middleware/location": {"name": "location", "paths": ["/api/Accounts/app", "/api/Apps/app", "/api/Settings/app"]}, "./lib/common/middleware/context-cleanup": {"name": "context-cleanup", "paths": ["/api/Accounts/app/token/refresh"], "enabled": true}}, "parse": {}, "routes": {"loopback#rest": {"paths": ["${restApiRoot}"]}}, "routes:after": {}, "files": {}, "final": {"loopback#urlNotFound": {}}, "final:after": {"./lib/common/middleware/error-handler": {"name": "error-handler", "paths": ["${restApiRoot}"], "enabled": true}, "strong-error-handler": {"params": {"debug": true, "log": true, "safeFields": ["code", "details"]}}}}