describe('----- Block -----', () => {
	describe('Invalid Token', () => {
		describe('Token Expired', () => {
			it('should token expired');
		});

		describe('Token Missing AccountId', () => {
			it('should block account');

			describe('Event emit', () =>
				it('should error'));
			// return expired token (invalid token);
		});
	});

	describe('Device Blocking', () => {
		describe('Device is blocked', () => {
			it('should block launch');

			describe('Event emit', () =>
				it('should Device blocked error'));
			// return expired token (invalid token);
		});

		describe('Device is not blocked', () => {
			it('should launch');
		});
	});

	describe('Account Activation/Deactivation', () => {
		describe('Account A Deactivation', () => {
			it('should deactivate account A');
		});

		describe('Account A Block Launch', () => {
			it('should block account A');

			describe('Event emit', () =>
				it('should Account deactivated error'));
			// return expired token (invalid token);
		});

		describe('Account A Activation', () => {
			before(() => {
				// Account B Registration
			});

			it('should activate account A');
		});

		describe('Account B login', () => {
			it('should still login account B');
		});

		describe('Account A login', () => {
			before(() => {
				// Account A Deactivation
			});

			it('should login account A');
		});
	});
});
