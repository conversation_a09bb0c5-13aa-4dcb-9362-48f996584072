const { assert, nock, M, SVC, waitForStartup } = appRequire('lib/common/mochaHelper'),
	helper = require('./helper/mocha');

describe('----- Password -----', () => {
	before(waitForStartup);
	before(nock.cleanAll);

	const ACCOUNT_NAME = '**********',
		PERSON_ID = 'person1',
		MOBILE = helper.givenMobile(),
		INSTALL = helper.givenInstallation(),
		DEVICE_1 = 'installationId',
		jwtDecode = helper.jwtDecode,
		jwtEncode = helper.jwtEncode,
		tenant = 'dev_test_perkd';

	let account, accountToken, response, installToken;

	before(() => {
		SVC.Person.findOrCreateByMobile({ id: PERSON_ID });
		SVC.Installation.findById({}, { id: DEVICE_1 });
		SVC.Installation.refresh();
		SVC.Message.create({ id: 'messageId' });

		installToken = jwtEncode(INSTALL);

		return SVC.Account.register()
			.set('perkd-install', installToken)
			.set('tenant-code', tenant)
			.send({ profile: { mobile: ACCOUNT_NAME } })
			.then(({ body: tokenItem }) => {
				accountToken = `x-access-token ${tokenItem.token}`;
				const accountId = jwtDecode(tokenItem.token).user.accountId;
				return M.Account.findById(accountId);
			})
			.then(acc => (account = acc));
	});

	describe('Request Password', () => {
		describe('should not send code w/o Token', () => {
			it('should not send code w/o Token', () => SVC.Account.requestPassword()
				.set('perkd-install', installToken)
				.send({ mobile: ACCOUNT_NAME })
				.then(res => (response = res)));

			describe('account', () => {
				it('should not sent code', () => assert.strictEqual(account.verifyCodes[0].purpose, M.Account.PURPOSE.register));
			});

			describe('response body', () => {
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 401));
			});
		});

		describe('should not send code w/ Unverified Account', () => {
			it('should not send code w/ Unverified Account', () => SVC.Account.requestPassword()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: ACCOUNT_NAME })
				.then(res => (response = res)));

			describe('account', () => {
				it('should not sent code', () => assert.strictEqual(account.verifyCodes[0].purpose, M.Account.PURPOSE.register));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_not_registered'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('should not send code w/ Invalid Country Code (999)', () => {
			it('should not send code w/ Invalid Country Code (999)', () => SVC.Account.requestPassword()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: MOBILE.invCtyCode })
				.then(res => (response = res)));

			describe('account', () => {
				it('should not sent code', () => assert.strictEqual(account.verifyCodes[0].purpose, M.Account.PURPOSE.register));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('should not send code w/ Invalid Mobile', () => {
			it('should not send code w/ Invalid Mobile', () => SVC.Account.requestPassword()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: MOBILE.inv })
				.then(res => (response = res)));

			describe('account', () => {
				it('should not sent code', () => assert.strictEqual(account.verifyCodes[0].purpose, M.Account.PURPOSE.register));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('should send code w/ Valid Mobile', () => {
			before(() => {
				const changes = {
					verifiedAt: new Date(),
					password: account.verifyCodes[0].code,
				};
				return account.updateAttributes(changes);
			});

			it('should send code w/ Valid Mobile', () => {
				SVC.Person.find([{ id: PERSON_ID }]);
				SVC.Message.create({ id: 'messageId' }); // sendVerifyCode
				SVC.Message.create({ id: 'messageId' }); // pushPassword

				return SVC.Account.requestPassword()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({ mobile: ACCOUNT_NAME })
					.then(res => (response = res));
			});

			describe('account', () => {
				before(() => M.Account.findById(account.id).then(acc => (account = acc)));

				it('should send code', () => assert.notStrictEqual(account.verifyCodes[0].code, undefined));
				it('should send code as password', () => assert.strictEqual(account.verifyCodes[0].purpose, 'password'));
			});

			describe('response body', () => it('should channel', () => assert.strictEqual(response.body.channel.sms, true)));
		});
	});

	describe('Change Password', () => {
		describe('should change password when login with vCode', () => {
			it('should login and change password', () => {
				SVC.Person.find([{ id: PERSON_ID }]);
				SVC.Installation.findById({}, { id: DEVICE_1 });
				SVC.Installation.refresh();
				SVC.Cache.resetCache();

				return SVC.Account.login()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({
						mobile: ACCOUNT_NAME,
						password: account.verifyCodes[0].code,
					})
					.then(res => (response = res));
			});

			describe('account', () => {
				before(() => M.Account.findById(account.id).then(acc => (account = acc)));

				it('should changed password', () => assert.strictEqual(account.password, account.verifyCodes[0].code));
				it('should verfication code usedAt', () => assert.notStrictEqual(account.verifyCodes[0].usedAt, null));
			});

			describe('response body', () => {
				it('should token', () => {
					const token = jwtDecode(response.body.token);
					assert.strictEqual(token.user.personId, PERSON_ID);
					assert.strictEqual(token.user.accountId, account.id.toString());
				});
			});
		});

		describe('should not change password w/o Token', () => {
			it('should not change password w/o Token', () => SVC.Account.changePassword()
				.set('perkd-install', installToken)
				.send({
					newPassword: '00000',
					oldPassword: '11111',
				})
				.then(res => (response = res)));

			describe('account', () => {
				before(() => M.Account.findById(account.id).then(acc => (account = acc)));

				it('should not changed password', () => assert.strictEqual(account.password, account.verifyCodes[0].code));
			});

			describe('response body', () => {
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 401));
			});
		});

		describe('should not change password w/ Invalid Old Password', () => {
			it('should not change password w/ Invalid Old Password', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.changePassword()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({
						newPassword: '00000',
						oldPassword: '00000',
					})
					.then(res => (response = res));
			});

			describe('account', () => {
				before(() => M.Account.findById(account.id).then(acc => (account = acc)));

				it('should not changed password', () => assert.strictEqual(account.password, account.verifyCodes[0].code));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_password_not_match'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('should change password w/ Valid Password (ignoreOld)', () => {
			it('should change password w/ Valid Password (ignoreOld)', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.changePassword()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({
						newPassword: '00000',
						oldPassword: '00000',
						ignoreOld: true,
					})
					.then(res => (response = res));
			});

			describe('account', () => {
				before(() => M.Account.findById(account.id).then(acc => (account = acc)));

				it('should changed password', () => assert.strictEqual(account.password, '00000'));
			});

			describe('response body', () => {
				it('should statusCode', () => assert.strictEqual(response.statusCode, 204));
			});
		});

		describe('should change password w/ Valid Password', () => {
			it('should change password w/ Valid Password', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.changePassword()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({
						newPassword: '12345',
						oldPassword: '00000',
					})
					.then(res => (response = res));
			});

			describe('account', () => {
				before(() => M.Account.findById(account.id).then(acc => (account = acc)));

				it('should changed password', () => assert.strictEqual(account.password, '12345'));
			});

			describe('response body', () => {
				it('should statusCode', () => assert.strictEqual(response.statusCode, 204));
			});
		});
	});
});
