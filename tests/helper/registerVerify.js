const { make } = appRequire('lib/common/mochaHelper'),
	shared = require('./mocha');

const givenApp = make({
	appID: 'me.perkd',
	version: {
		major: 4,
		minor: 0,
	},
	settings: {
		customCard: {
			imageUpload: {
				enabledCountries: [],
				disabledCountries: [],
				enabled: true,
			},
			share: {
				enabledCountries: [],
				disabledCountries: [],
				enabled: true,
			},
		},
	},
	name: 'application',
});

Object.assign(exports, shared, {
	givenApp,
});
