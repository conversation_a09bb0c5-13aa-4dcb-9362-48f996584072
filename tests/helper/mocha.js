const { make } = appRequire('lib/common/mochaHelper'),
	{ jwt } = appRequire('lib/common/helper');

const givenMobile = make({
	new: '6597812930',
	reg: '6597812931',
	invCtyCode: '99993915787',
	inv: '65978129296597810000',
});

const givenInstallation = make({
	app: { id: 'me.perkd', version: '4.0' },
	device: { uuid: 'installationId' },
	carrier: { country: 'SG' },
	locale: { language: 'en', country: 'SG', timeZone: 8000 },
});

const jwtDecode = encoded => JSON.parse(new jwt().decode(encoded).payload);

const jwtEncode = item => new jwt().encode(item);

Object.assign(exports, {
	givenMobile,
	givenInstallation,
	jwtDecode,
	jwtEncode,
});
