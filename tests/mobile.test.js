const { assert, nock, M, SVC, waitForStartup } = appRequire('lib/common/mochaHelper'),
	helper = require('./helper/mocha');

describe('----- Mobile -----', () => {
	before(waitForStartup);
	before(nock.cleanAll);

	const ACCOUNT_NAME = '**********',
		PERSON_ID = 'person1',
		MOBILE = helper.givenMobile(),
		INSTALL = helper.givenInstallation(),
		DEVICE_1 = 'installationId',
		jwtDecode = helper.jwtDecode,
		jwtEncode = helper.jwtEncode,
		tenant = 'dev_test_perkd';

	let account, accountToken, response, installToken;

	before(() => {
		SVC.Person.findOrCreateByMobile({ id: PERSON_ID });
		SVC.Installation.findById({}, { id: DEVICE_1 });
		SVC.Installation.refresh();
		SVC.Message.create({ id: 'messageId' });

		installToken = jwtEncode(INSTALL);

		return SVC.Account.register()
			.set('perkd-install', installToken)
			.set('tenant-code', tenant)
			.send({ profile: { mobile: ACCOUNT_NAME } })
			.then(res => {
				accountToken = `x-access-token ${res.body.token}`;
				const accountId = jwtDecode(res.body.token).user.accountId;
				return M.Account.findById(accountId);
			})
			.then(acc => (account = acc));
	});

	describe('should not send code w/o token', () => {
		it('should not send code w/o Token ', () => SVC.Account.changeMobile()
			.set('perkd-install', installToken)
			.send({ mobile: ACCOUNT_NAME })
			.then(res => (response = res)));

		describe('account', () => {
			it('should not sent code', () => assert.strictEqual(account.verifyCodes[0].purpose, M.Account.PURPOSE.register));
		});

		describe('response body', () => {
			it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 401));
		});
	});

	describe('should not send code w/ Invalid Country Code (999)', () => {
		it('should not send code w/ Invalid Country Code (999)', () => {
			SVC.Person.find([{ id: PERSON_ID }]);

			return SVC.Account.changeMobile()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: MOBILE.invCtyCode })
				.then(res => (response = res));
		});

		describe('account', () => {
			it('should not sent code', () => assert.strictEqual(account.verifyCodes[0].purpose, M.Account.PURPOSE.register));
		});

		describe('response body', () => {
			it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_invalid'));
			it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
		});
	});

	// it.skip('should not send code w/ Invalid Mobile ', () => {
	// 	return request(ACCOUNT.host).post(ACCOUNT.api + '/app/mobile?access_token=' + token)
	// 		.send({ mobile: mobile.inv })
	// 		.then(({ body: err }) => {
	// 			assert.strictEqual(err.error.statusCode, 400);
	// 			assert.strictEqual(err.error.code, 'Invalid mobile');
	// 		});
	// });

	describe('should not send code w/ Same Mobile', () => {
		it('should not send code w/ Same Mobile ', () => {
			SVC.Person.find([{ id: PERSON_ID }]);

			return SVC.Account.changeMobile()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: ACCOUNT_NAME })
				.then(res => (response = res));
		});

		describe('account', () => {
			it('should not sent code', () => assert.strictEqual(account.verifyCodes[0].purpose, M.Account.PURPOSE.register));
		});

		describe('response body', () => {
			it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_same'));
			it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
		});
	});

	describe('should not send code w/ Registered Mobile', () => {
		before(() => {
			SVC.Person.findOrCreateByMobile({ id: PERSON_ID });
			SVC.Installation.findById({}, { id: DEVICE_1 });
			SVC.Installation.refresh();
			SVC.Message.create({ id: 'messageId' });

			return SVC.Account.register()
				.set('perkd-install', installToken)
				.set('tenant-code', tenant)
				.send({ profile: { mobile: MOBILE.reg } })
				.then(res => {
					const accountId = jwtDecode(res.body.token).user.accountId;
					return M.Account.findById(accountId);
				})
				.then(acc => acc.updateAttributes({ verifiedAt: new Date() }));
		});

		it('should not send code w/ Registered Mobile', () => {
			SVC.Person.find([{ id: PERSON_ID }]);

			return SVC.Account.changeMobile()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: MOBILE.reg })
				.then(res => (response = res));
		});

		describe('account', () => {
			it('should not sent code', () => assert.strictEqual(account.verifyCodes[0].purpose, M.Account.PURPOSE.register));
		});

		describe('response body', () => {
			it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_in_use'));
			it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
		});
	});

	describe('should send code w/ Unregistered Mobile', () => {
		it('should send code w/ Unregistered Mobile', () => {
			SVC.Person.find([{ id: PERSON_ID }]);
			SVC.Message.create({ id: 'messageId' });

			return SVC.Account.changeMobile()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: MOBILE.new })
				.then(res => (response = res));
		});

		describe('account', () => {
			before(() => M.Account.findById(account.id).then(acc => (account = acc)));

			it('should sent code', () => assert.strictEqual(account.verifyCodes[0].purpose, M.Account.PURPOSE.changeMobile));
		});

		describe('response body', () => it('should statusCode', () => assert.strictEqual(response.statusCode, 200)));
	});

	describe('Verify Code to Change Mobile', () => {
		describe('should not change mobile w/o Token', () => {
			it('should not change mobile w/o Token', () => SVC.Account.verifyChangeMobile()
				.set('perkd-install', installToken)
				.send({ mobile: ACCOUNT_NAME, code: '00000' })
				.then(res => (response = res)));

			describe('account', () => {
				it('should not change mobile', () => assert.strictEqual(account.name, ACCOUNT_NAME));
			});

			describe('response body', () => {
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 401));
			});
		});

		describe('should not change mobile w/ Invalid Country Code (999)', () => {
			it('should not change mobile w/ Invalid Country Code (999)', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.verifyChangeMobile()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({
						mobile: MOBILE.invCtyCode,
						code: '00000',
					})
					.then(res => (response = res));
			});

			describe('account', () => {
				it('should not change mobile', () => assert.strictEqual(account.name, ACCOUNT_NAME));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		// it.skip('should not change mobile w/ Invalid Mobile ', () => {
		// 	nock(PERSON.host).get(PERSON.api).times(1).query(true)
		// 		.reply(200, [{ id: personId }]);

		// 	return request(ACCOUNT.host).post(ACCOUNT.api + '/app/mobile/verify?access_token=' + token)
		// 		.send({ mobile: mobile.inv, code: '12345' })
		// 		.then(({ body: err }) => {
		// 			console.log(err);

		// 			assert.strictEqual(err.error.code, 'account_mobile_invalid');
		// 			assert.strictEqual(err.error.statusCode, 400);
		// 			assert.strictEqual(err.error.code, 'Invalid mobile');
		// 		});
		// });

		describe('should not change mobile w/ Same Mobile', () => {
			it('should not change mobile w/ Same Mobile', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.verifyChangeMobile()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({
						mobile: ACCOUNT_NAME,
						code: '00000',
					})
					.then(res => (response = res));
			});

			describe('account', () => {
				it('should not change mobile', () => assert.strictEqual(account.name, ACCOUNT_NAME));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_vcode_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('should not change mobile w/ Invalid Code', () => {
			it('should not change mobile w/ Invalid Code', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.verifyChangeMobile()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({
						mobile: MOBILE.new,
						code: '00000',
					})
					.then(res => (response = res));
			});

			describe('account', () => {
				it('should not change mobile', () => assert.strictEqual(account.name, ACCOUNT_NAME));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_vcode_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('should change mobile w/ Valid Code', () => {
			it('should change mobile w/ Valid Code', () => {
				SVC.Person.find([{ id: PERSON_ID }]);
				const verifyCode = account.verifyCodes[0].code;

				return SVC.Account.verifyChangeMobile()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({
						mobile: MOBILE.new,
						code: verifyCode,
					})
					.then(res => (response = res));
			});

			describe('account', () => {
				before(() => M.Account.findById(account.id).then(acc => (account = acc)));

				it('should change mobile', () => assert.strictEqual(account.name, MOBILE.new));
				it('should verfication code usedAt', () => assert.notStrictEqual(account.verifyCodes[0].usedAt, null));
			});

			describe('response body', () => {
				it('should statusCode', () => assert.strictEqual(response.statusCode, 204));
			});
		});
	});

	// before(() => {
	// 	const models = [Account];
	// 	installToken = jwtEncode(INSTALL);

	// 	return app.init(null, true)
	// 		.then(() => destroyAll(models))
	// 		.then(createTestData);

	// 	function createTestData() {
	// 		Person.mock('doUpsert', undefined, { id: PERSON_ID });
	// 		Installation.mock('findById', DEVICE_1);
	// 		Installation.mock('refresh');
	// 		Message.mock('create');

	// 		return request(ACCOUNT.host).post(ACCOUNT.api + '/app/register')
	// 			.set('perkd-install', installToken)
	// 			.set('tenant-code', tenant)
	// 			.send({ profile: { mobile: ACCOUNT_NAME } })
	// 			.then(res => {
	// 				accountToken = `x-access-token ${res.body.token}`;
	// 				const accountId = jwtDecode(res.body.token).user.accountId;
	// 				return Account.findById(accountId);
	// 			})
	// 			.then(acc => { account = acc; });
	// 	}
	// });
});
