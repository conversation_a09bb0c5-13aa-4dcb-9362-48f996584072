const { app, assert, nock, M, SVC, waitForStartup } = appRequire('lib/common/mochaHelper'),
	helper = require('./helper/mocha');

describe('----- Auth -----', () => {
	before(waitForStartup);
	before(nock.cleanAll);

	const ACCOUNT_NAME = '**********',
		PERSON_ID = 'person1',
		MOBILE = helper.givenMobile(),
		INSTALL = helper.givenInstallation(),
		DEVICE_1 = 'installationId',
		jwtDecode = helper.jwtDecode,
		jwtEncode = helper.jwtEncode,
		tenant = 'dev_test_perkd';

	let account, accountToken, response, installToken;

	before(() => {
		SVC.Person.findOrCreateByMobile({ id: PERSON_ID });
		SVC.Installation.findById({}, { id: DEVICE_1 });
		SVC.Installation.refresh();
		SVC.Message.create({ id: 'messageId' });

		installToken = jwtEncode(INSTALL);

		return SVC.Account.register()
			.set('perkd-install', installToken)
			.set('tenant-code', tenant)
			.send({ profile: { mobile: ACCOUNT_NAME } })
			.then(({ body: tokenItem }) => {
				accountToken = `x-access-token ${tokenItem.token}`;
				const accountId = jwtDecode(tokenItem.token).user.accountId;
				return M.Account.findById(accountId);
			})
			.then(acc => {
				INSTALL.accountId = acc.id;
				account = acc;
			});
	});

	describe('Launch', () => {
		let event;
		app.on('xaccount.account.launched', evt => (event = evt));

		it('should LAUNCH (API)', () => {
			SVC.Installation.upsert();

			return SVC.Account.launch()
				.set('perkd-install', installToken)
				.set('tenant-code', tenant)
				.then(res => (response = res));
		});

		describe('response', () => {
			it('should statusCode', () => assert.strictEqual(response.statusCode, 204));
		});

		describe('event', () => {
			it('should timestamp', () => assert.strictEqual(event.timestamp <= new Date(), true));
		});
	});

	describe('Invalid Login', () => {
		describe('Login denied without mobile', () => {
			it('should not LOGIN w/o Mobile', () => SVC.Account.login()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: '' })
				.then(res => (response = res)));

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_password_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('Login denied with invalid country code', () => {
			it('should not LOGIN w/ Invalid Country Code (999)', () => SVC.Account.login()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: MOBILE.invCtyCode })
				.then(res => (response = res)));

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});
	});

	describe('Login with biometrics', () => {
		after(() => account.updateAttributes({ verifiedAt: null }));

		describe('Login denied without token', () => {
			it('should not LOGIN w/o Token', () => SVC.Account.login()
				.set('perkd-install', installToken)
				.set('tenant-code', tenant)
				.send({ mobile: ACCOUNT_NAME, password: undefined, touchID: true })
				.then(res => (response = res)));

			describe('response body', () => {
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 500));
			});
		});

		describe('Login', () => {
			before(() => account.updateAttributes({ verifiedAt: new Date() }));

			it('should LOGIN', () => {
				SVC.Person.find([{ id: PERSON_ID }]);
				SVC.Installation.findById({}, { id: DEVICE_1 });
				SVC.Installation.refresh();
				SVC.Cache.resetCache();

				return SVC.Account.login()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({ mobile: ACCOUNT_NAME, password: undefined, touchID: true })
					.then(res => (response = res));
			});

			describe('response body', () => {
				it('should token', () => {
					const token = jwtDecode(response.body.token);
					assert.strictEqual(token.user.personId, PERSON_ID);
					assert.strictEqual(token.user.accountId, account.id.toString());
				});
			});
		});
	});

	describe('Login with password', () => {
		let code;

		describe('Login denied with unverified account', () => {
			it('should not LOGIN w/ Unverified Account', () => SVC.Account.login()
				.set('perkd-install', installToken)
				.set('Authorization', accountToken)
				.send({ mobile: ACCOUNT_NAME })
				.then(res => (response = res)));

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_password_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('Login denied with incorrect password', () => {
			before(() => {
				code = account.verifyCodes[0].code;
				const changes = {
					verifiedAt: new Date(),
					password: code,
				};
				return account.updateAttributes(changes);
			});

			it('should not LOGIN w/ Incorrect Password', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.login()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({ mobile: ACCOUNT_NAME, password: '00000' })
					.then(res => (response = res));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_password_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('Login with correct password', () => {
			it('should LOGIN w/ Correct Password', () => {
				SVC.Person.find([{ id: PERSON_ID }]);
				SVC.Installation.findById({}, { id: DEVICE_1 });
				SVC.Installation.refresh();
				SVC.Cache.resetCache();

				return SVC.Account.login()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({ mobile: ACCOUNT_NAME, password: code })
					.then(res => (response = res));
			});

			describe('response body', () => {
				it('should token', () => {
					const token = jwtDecode(response.body.token);
					assert.strictEqual(token.user.personId, PERSON_ID);
					assert.strictEqual(token.user.accountId, account.id.toString());
				});
			});
		});

		describe('Login with changed password', () => {
			let vCode;

			before(() => {
				SVC.Message.create({ id: 'messageId' });
				return account.sendVerifyCode('password', 'sms').then(codeItem => (vCode = codeItem.code));
			});

			it('should LOGIN w/ Changed Password', () => {
				SVC.Person.find([{ id: PERSON_ID }]);
				SVC.Installation.findById({}, { id: DEVICE_1 });
				SVC.Installation.refresh();
				SVC.Cache.resetCache();

				return SVC.Account.login()
					.set('perkd-install', installToken)
					.set('Authorization', accountToken)
					.send({ mobile: ACCOUNT_NAME, password: vCode })
					.then(res => (response = res));
			});

			describe('response body', () => {
				it('should token', () => {
					const token = jwtDecode(response.body.token);
					assert.strictEqual(token.user.personId, PERSON_ID);
					assert.strictEqual(token.user.accountId, account.id.toString());
				});
			});
		});
	});

	describe('Logout', () => {
		let event;
		app.on('xaccount.account.logout', evt => (event = evt));

		it('should LOGOUT', () => {
			SVC.Installation.upsert();

			return SVC.Account.logout()
				.set('Authorization', accountToken)
				.then(res => (response = res));
		});

		describe('response', () => {
			it('should statusCode', () => assert.strictEqual(response.statusCode, 204));
		});

		describe('event', () => {
			it('should timestamp', () => assert.strictEqual(event.timestamp <= new Date(), true));
		});
	});
});
