const { assert, nock, M, SVC, waitForStartup } = appRequire('lib/common/mochaHelper'),
	helper = require('./helper/registerVerify');

describe('----- Register & Verify -----', () => {
	before(waitForStartup);
	before(nock.cleanAll);

	const ACCOUNT_NAME = '**********',
		PERSON_ID = 'person1',
		MOBILE = helper.givenMobile(),
		INSTALL = helper.givenInstallation(),
		DEVICE_1 = 'installationId',
		jwtDecode = helper.jwtDecode,
		jwtEncode = helper.jwtEncode,
		tenant = 'dev_test_perkd',
		PERKDID_CARDMASTER_ID = '000000000000000000000000';

	let oldAccount, newAccount, invalidAccount, newAccountToken, installToken, response;

	before(() => {
		installToken = jwtEncode(INSTALL);

		return M.App.create(helper.givenApp());
	});

	describe('REGISTER', () => {
		describe('should not REGISTER w/o mobile', () => {
			it('should not REGISTER w/o mobile', () => SVC.Account.register()
				.set('perkd-install', installToken)
				.set('tenant-code', tenant)
				.send({ profile: {} })
				.then(res => (response = res)));

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_required'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('should not REGISTER invalid country code(999)', () => {
			it('should not REGISTER invalid country code(999)', () => SVC.Account.register()
				.set('perkd-install', installToken)
				.set('tenant-code', tenant)
				.send({ profile: { mobile: MOBILE.invCtyCode } })
				.then(res => (response = res)));

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_mobile_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('should REGISTER w/ invalid mobile (skipValidate)', () => {
			it('should REGISTER w/ invalid mobile (skipValidate)', () => {
				SVC.Person.findOrCreateByMobile({ id: PERSON_ID });
				SVC.Installation.findById({}, { id: DEVICE_1 });
				SVC.Installation.refresh();
				SVC.Message.create({ id: 'messageId' });

				return SVC.Account.register()
					.set('perkd-install', installToken)
					.set('tenant-code', tenant)
					.send({
						profile: { mobile: MOBILE.inv },
						options: { skipValidate: true },
					})
					.then(res => {
						const accountId = jwtDecode(res.body.token).user.accountId;
						response = res;
						return M.Account.findById(accountId);
					})
					.then(acc => (invalidAccount = acc));
			});

			describe('account', () => {
				it('should invalid mobile number', () => assert.strictEqual(invalidAccount.name, MOBILE.inv));
				it('should send code as register', () => assert.strictEqual(invalidAccount.verifyCodes[0].purpose, M.Account.PURPOSE.register));
			});

			describe('response body', () => {
				it('should token', () => assert.notStrictEqual(response.body.token, undefined));
			});
		});

		describe('should REGISTER w/ mobile', () => {
			it('should REGISTER w/ mobile', () => {
				SVC.Person.findOrCreateByMobile({ id: PERSON_ID });
				SVC.Installation.findById({}, { id: DEVICE_1 });
				SVC.Installation.refresh();
				SVC.Message.create({ id: 'messageId' });

				return SVC.Account.register()
					.set('perkd-install', installToken)
					.set('tenant-code', tenant)
					.send({ profile: { mobile: ACCOUNT_NAME } })
					.then(res => {
						const accountId = jwtDecode(res.body.token).user.accountId;
						response = res;
						return M.Account.findById(accountId);
					})
					.then(acc => (oldAccount = acc));
			});

			describe('account', () => {
				it('should sent code', () => assert.strictEqual(oldAccount.verifyCodes[0].purpose, M.Account.PURPOSE.register));
				it('should mobile number', () => assert.strictEqual(oldAccount.name, ACCOUNT_NAME));
			});

			describe('response body', () => {
				it('should token', () => assert.notStrictEqual(response.body.token, undefined));
			});
		});
	});

	describe('Re-REGISTER', () => {
		describe('should re-register with same mobile', () => {
			it('should re-register with same mobile', () => {
				SVC.Person.findOrCreateByMobile({ id: PERSON_ID });
				SVC.Installation.findById({}, { id: DEVICE_1 });
				SVC.Installation.refresh();
				SVC.Message.create({ id: 'messageId' });

				return SVC.Account.register()
					.set('perkd-install', installToken)
					.set('tenant-code', tenant)
					.send({ profile: { mobile: ACCOUNT_NAME } })
					.then(res => {
						const accountId = jwtDecode(res.body.token).user.accountId;
						response = res;
						return M.Account.findById(accountId);
					})
					.then(acc => (oldAccount = acc));
			});

			describe('account', () => {
				it('should sent code', () => assert.strictEqual(oldAccount.verifyCodes[0].purpose, M.Account.PURPOSE.register));
				it('should mobile number', () => assert.strictEqual(oldAccount.name, ACCOUNT_NAME));
			});

			describe('response body', () => {
				it('should token', () => assert.notStrictEqual(response.body.token, undefined));
			});
		});

		describe('should re-register with different mobile', () => {
			it('should re-register with different mobile', () => {
				SVC.Installation.findById({ accountId: oldAccount.id, personId: PERSON_ID }, { id: DEVICE_1 });
				SVC.Installation.refresh();
				SVC.Person.deleteById({}, { id: PERSON_ID });
				SVC.Message.create({ id: 'messageId' });

				return SVC.Account.register()
					.set('perkd-install', installToken)
					.set('tenant-code', tenant)
					.send({ profile: { mobile: MOBILE.reg } })
					.then(res => {
						newAccountToken = `x-access-token ${res.body.token}`;
						const accountId = jwtDecode(res.body.token).user.accountId;
						response = res;
						return M.Account.findById(accountId);
					})
					.then(acc => (newAccount = acc));
			});

			describe('old account', () => {
				before(() => M.Account.findById(oldAccount.id).then(acc => (oldAccount = acc)));

				it('should deleted oldAccount', () => assert.strictEqual(oldAccount, null));
			});

			describe('account', () => {
				it('should sent code', () => assert.strictEqual(newAccount.verifyCodes[0].purpose, M.Account.PURPOSE.register));
				it('should mobile number', () => assert.strictEqual(newAccount.name, MOBILE.reg));
			});

			describe('response body', () => {
				it('should token', () => assert.notStrictEqual(response.body.token, undefined));
			});
		});

		describe('should not re-register w/ verified', () => {
			before(() => invalidAccount.updateAttributes({ verifiedAt: new Date() }));

			it('should not re-register w/ verified', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.register()
					.set('perkd-install', installToken)
					.set('tenant-code', tenant)
					.send({
						profile: { mobile: MOBILE.inv },
						options: { skipValidate: true },
					})
					.then(res => (response = res));
			});

			describe('account', () => {
				it('should not sent code', () => assert.strictEqual(invalidAccount.verifyCodes[0].purpose, M.Account.PURPOSE.register));
				it('should have 1 code', () => assert.strictEqual(invalidAccount.verifyCodes.length, 1));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 605));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});
	});

	describe('VERIFY', () => {
		describe('should not VERIFY w/o Token', () => {
			it('should not VERIFY w/o Token', () => SVC.Account.verify()
				.set('perkd-install', installToken)
				.send({ code: newAccount.verifyCodes[0].code })
				.then(res => (response = res)));

			describe('account', () => {
				before(() => M.Account.findById(newAccount.id).then(acc => (newAccount = acc)));

				it('should not verified', () => assert.strictEqual(newAccount.verifiedAt, null));
			});

			describe('response body', () => {
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 401));
			});
		});

		describe('should not VERIFY w/o Code', () => {
			it('should not VERIFY w/o Code', () => SVC.Account.verify()
				.set('perkd-install', installToken)
				.set('Authorization', newAccountToken)
				.send({ code: '' })
				.then(res => (response = res)));

			describe('account', () => {
				before(() => M.Account.findById(newAccount.id).then(acc => (newAccount = acc)));

				it('should not verified', () => assert.strictEqual(newAccount.verifiedAt, null));
			});

			describe('response body', () => {
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('Invalid Code', () => {
			it('should not VERIFY with invalid code', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.verify()
					.set('perkd-install', installToken)
					.set('Authorization', newAccountToken)
					.send({ code: '00000' })
					.then(res => (response = res));
			});

			describe('account', () => {
				before(() => M.Account.findById(newAccount.id).then(acc => (newAccount = acc)));

				it('should not verified', () => assert.strictEqual(newAccount.verifiedAt, null));
			});

			describe('response body', () => {
				it('should code', () => assert.strictEqual(response.body.error.code, 'account_vcode_invalid'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});

		describe('should VERIFY with Code', () => {
			it('should VERIFY with Code', () => {
				SVC.Person.find([{ id: PERSON_ID }]);
				SVC.Installation.upsert();
				SVC.CardMaster.issue({ id: PERKDID_CARDMASTER_ID });
				SVC.Card.find([{ id: 'cardId', cardMaster: {} }]);

				return SVC.Account.verify()
					.set('perkd-install', installToken)
					.set('Authorization', newAccountToken)
					.send({ code: newAccount.verifyCodes[0].code })
					.then(res => (response = res));
			});

			describe('account', () => {
				before(() => M.Account.findById(newAccount.id).then(acc => (newAccount = acc)));

				it('should verify code for correct mobile number', () => assert.strictEqual(newAccount.verifyCodes[0].mobile, MOBILE.reg));
				it('should verify code for register purpose', () => assert.strictEqual(newAccount.verifyCodes[0].purpose, M.Account.PURPOSE.register));
				it('should verfication code usedAt', () => assert.notStrictEqual(newAccount.verifyCodes[0].usedAt, null));
				it('should verified', () => assert.notStrictEqual(newAccount.verifiedAt, null));
			});

			describe('response body', () => {
				it('should return PerkdId cardId', () => assert.strictEqual(response.body.startup.id, 'cardId'));
			});
		});

		describe.skip('Verified Account', () => {
			it('should not VERIFY with verified account', () => {
				SVC.Person.find([{ id: PERSON_ID }]);

				return SVC.Account.verify()
					.set('perkd-install', installToken)
					.set('Authorization', newAccountToken)
					.send({ code: newAccount.verifyCodes[0].code })
					.then(res => (response = res));
			});

			describe('response body', () => {
				it('should code', () =>	assert.strictEqual(response.body.error.code, 'account_mobile_has_registered'));
				it('should statusCode', () => assert.strictEqual(response.body.error.statusCode, 400));
			});
		});
	});

	// describe('Send verification code (Sign Up)', () => {
	// it('should not send more than 5 sends daily', () => {
	// 	return recursiveRequestVerifyCode(token, ACCOUNT_SEND_VERIFY_CODE_PARAM)
	// 		.catch(err => { assert(err); });
	// });

	// it('should hit daily limit', () => {
	// 	const accountId = jwtDecode(token, 'account');
	// 	return Account.findById(accountId).then(account => {
	// 		assert.strictEqual(account.limit.type.vCode.requestCount1, 5);
	// 	});
	// });

	// function recursiveRequestVerifyCode(token, param, times = 5) {
	// 	const requests = [];
	// 	for (let i = 0; i < times; i++) { requests.push([i, token, param]); }
	// 	return Promise.map(requests, (params) => {
	// 		return request('http://127.0.0.1:8150').post('/api/Accounts/requestVerifyCode?access_token=' + params[1])
	// 			.send(params[2])
	// 			.then(({ body: instance }) => {
	// 				if (params[0] === 4) ENVIRONMENT.code = instance.code;
	// 				return Promise.resolve(instance);
	// 			})
	// 			.catch(err => Promise.reject(err));
	// 	}, { concurrency: 1 });
	// }
	// });

	/*

	// 11-searchRegion
	describe.skip('Search Accounts by Region', () => {
		it('should find Accounts', () => {
			return request('http://127.0.0.1:8150').get('/api/Accounts/searchRegion?access_token=' + ENVIRONMENT.token + '&searchString=' + ACCOUNT.SEARCH_STRING)
				.then(({ body: instance }) => {
					assert.equal(instance[0].name, ACCOUNT.SEARCH_STRING);
				});
		});
	});

	// 13-deleteAccount
	describe.skip('Delete Account', () => {
		it('should update deletedAt (account)', () => {
			return request('http://127.0.0.1:8150').post('/api/Accounts/delete?access_token=' + ENVIRONMENT.token)
				.then(({ body: instance }) => {
					assert(instance.deletedAt);
				});
		});
	});

	// 14-deactivateAccount
	describe.skip('Deactivate Account', () => {
		it('should update deactivatedAt (account)', () => {
			const PERSON_FIND_ONE_RES = PERSON.findOne(
					PERSON.IDS[0], PERSON.MOBILE_FULL_NUMBERS[0], PERSON.MOBILE_NUMBERS[0]),
				PERSON_ID_RES = PERSON_ID(
					PERSON.IDS[0], PERSON.MOBILE_FULL_NUMBERS[0], PERSON.DELETED_AT.deleted);

			nock('http://127.0.0.1:3101').get('/api/Persons/findOne').times(1).query(true)
				.reply(200, PERSON_FIND_ONE_RES);
			nock('http://127.0.0.1:3101').patch('/api/Persons/' + PERSON.IDS[0]).times(1)
				.reply(() => [200, PERSON_ID_RES]);
			nock('http://127.0.0.1:3101').post('/api/Persons/' + PERSON.IDS[0] + '/deletePhone').times(1)
				.reply(200);

			const accountId = jwtDecode(ENVIRONMENT.token, 'account');
			return Account.findById(accountId).then(account => {
				return account.deactivate().then(res => {
					assert(res.deactivatedAt);
				});
			});
		});
	});

	*/
});
