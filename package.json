{"name": "x-account", "description": "Account service for Perkd App", "version": "1.0.0", "private": true, "engines": {"node": ">=22"}, "main": "server/server.js", "scripts": {"lint": "eslint .", "start": "node .", "test": "node --test tests/**/*.test.js", "test-one": "node --test", "sub": "git submodule foreach 'git pull origin master && git checkout master && git pull'", "docker": "docker container kill xaccount || true && docker container rm xaccount || true && docker build --build-arg DOCKER_ENV=test --tag xaccount:dev . && docker run -it --name xaccount -p 8150:8150 --network dev xaccount:dev", "update": "ncu -u", "reinstall": "(rm -rf node_modules/ || true) && (rm yarn.lock || true) && yarn install"}, "repository": {"type": "git", "url": "https://github.com/perkd/x-account.git"}, "dependencies": {"@crm/loopback": "github:perkd/crm-loopback#semver:^0.8.2", "@crm/types": "github:perkd/crm-types#semver:^1.11.19", "@perkd/api-request": "github:perkd/api-request#semver:^2.0.2", "@perkd/errors": "github:perkd/errors#semver:^0.5.1", "@perkd/event-registry-perkd": "github:perkd/event-registry-perkd#semver:^1.1.22", "@perkd/eventbus": "github:perkd/eventbus#semver:^4.9.5", "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0", "@perkd/metrics-push": "github:perkd/metrics-push#semver:^1.5.0", "@perkd/multitenant-context": "github:perkd/multitenant-context#semver:0.6.3", "@perkd/passkey": "github:perkd/passkey#semver:^0.6.0", "@perkd/settings": "github:perkd/settings#semver:^1.6.0", "@perkd/sync": "github:perkd/sync#semver:^1.1.5", "@perkd/tenants": "github:perkd/tenants#semver:^4.9.0", "@perkd/touchpoints": "github:perkd/touchpoints#semver:^0.2.4", "@perkd/utils": "github:perkd/utils#semver:^2.0.5", "@perkd/wallet": "github:perkd/wallet#semver:^0.5.0", "@perkd/wallet-regions": "github:perkd/wallet-regions#semver:^0.4.0", "async-lock": "^1.4.1", "aws-sdk": "^2.1692.0", "colors": "^1.4.0", "compression": "^1.8.0", "cors": "^2.8.5", "deep-diff": "^1.0.2", "dotenv": "^17.0.0", "geolib": "^3.3.4", "loopback": "github:perkd/loopback#semver:3.34.2", "loopback-boot": "^3.3.1", "loopback-connector-mongodb": "github:perkd/loopback-connector-mongodb#semver:^6.3.1", "loopback-filters": "^1.1.1", "redlock": "5.0.0-beta.2", "semver": "^7.7.2"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/eslint-parser": "^7.27.5", "@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3", "@slack/web-api": "^7.9.3", "@stylistic/eslint-plugin-js": "^4.4.1", "debug": "^4.4.1", "eslint": "^9.30.0", "eslint-plugin-jsonc": "^2.20.1", "eslint-plugin-n": "^17.20.0", "eslint-plugin-security": "^3.0.1", "loopback-component-explorer": "^6.5.1"}, "resolutions": {"loopback-datasource-juggler": "github:perkd/loopback-datasource-juggler#semver:^5.2.0", "strong-remoting": "github:perkd/strong-remoting#semver:3.20.2", "strong-globalize": "6.0.6"}, "packageManager": "yarn@4.9.2"}