# Account Service for Perkd
Account is a microservice of the Perkd platform, handling user account management and related functionalities.

# Table of Contents
- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Core Features](#core-features)
- [User Account Management](#user-account-management)
- [Verification System](#verification-system)
- [Authentication & Authorization](#authentication--authorization)
- [Settings Management](#settings-management)
- [Anti-hacking](#anti-hacking)
- [Service Integration](#service-integration)
- [Data Models](#data-models) - [API Endpoints](#api-endpoints)
- [Events](#events)

## Overview
The Account Service is a critical component of the Perkd platform, responsible for managing user accounts. It facilitates user registration, authentication, and profile management, providing a secure and seamless experience for users of the Perkd App.

### System Architecture

```mermaid
graph TD
    subgraph Account Service
        subgraph API Layer
            AccountAPI[Account APIs]
            AdminAPI[Admin APIs]
            AuthAPI[Auth APIs]
            SettingsAPI[Settings APIs]
            SyncAPI[Sync APIs]
        end

        subgraph Authentication Management
            TokenAuth[Token-based Auth]
            BiometricAuth[Biometric Auth]
            SecurityCtrl[Security Controls]
            AntiHacking[Anti-hacking]
        end

        subgraph Account Management
            Profile[Profile Management]
            Registration[Mobile Registration]
            Verification[Verification System]
        end

        subgraph Settings Management
            SettingsModel[Settings & Config]
            VersionMgmt[Version Control]
            EventBus[Event Bus]
        end

        subgraph Cache Layer
            RedisCache[Redis Cache]
            LocalCache[Local Cache]
        end

        SessionMgmt[Session Management]
        SyncEngine[Sync Engine]

        %% API to Core Management
        AuthAPI --> TokenAuth
        AuthAPI --> BiometricAuth
        AccountAPI --> Registration
        AccountAPI --> Profile
        AdminAPI --> SecurityCtrl
        AdminAPI --> Profile
        SettingsAPI --> SettingsModel
        SyncAPI --> SyncEngine

        %% Core Management Interactions
        TokenAuth --> SessionMgmt
        BiometricAuth --> SessionMgmt
        Registration --> SessionMgmt
        
        %% Settings Management Interactions
        SettingsModel <--> RedisCache
        SettingsModel --> VersionMgmt
        SettingsModel <--> EventBus
        
        %% Sync Engine Interactions
        Profile --> SyncEngine
        SessionMgmt --> SyncEngine
        SyncEngine <--> RedisCache
        
        %% Cache Interactions
        Profile --> RedisCache
        SessionMgmt --> RedisCache
        Verification --> LocalCache

        %% Security Interactions
        SecurityCtrl --> AntiHacking
        AntiHacking --> RedisCache
        AuthAPI --> AntiHacking
        AccountAPI --> AntiHacking
        Registration --> AntiHacking
    end
```

- Account APIs: Registration and profile operations for Perkd App
- Auth APIs: Authentication and authorization requests for Perkd App
- Admin APIs: Administrative and security controls for Admin Portal
- Settings APIs: Settings management for Admin Portal
- Sync APIs: Data synchronization endpoints


### Core Features

#### User Account Management
- Mobile number-based account registration and verification
- Profile and preferences management
- Password and security settings
- Multi-language support
- Installation and device management
- Data caching and synchronization
- Profile data management:
  - Mobile number with verification
  - Password with expiration periods
  - Language and region preferences
  - Custom display settings
  - Communication preferences
  - Passkey credentials for supported devices

#### Authentication & Authorization
- Secure token-based authentication with AWS Cognito
- Passkey (WebAuthn) support for passwordless authentication
  - FIDO2-compliant implementation for enhanced security
  - Platform authenticator integration on supported devices
  - Multi-device credential synchronization
  - Native mobile SDKs for iOS and Android
  - Cross-platform support (React Native, Flutter)
  - For detailed documentation, see [Passkey Authentication](docs/passkey.md)
- Biometric authentication support (TouchID/FaceID)
- Multi-device session management
- Security controls with anti-hacking protection:
  - Brute force prevention
  - Device security (fingerprinting, validation)
  - Access control (country/carrier whitelisting)
  - Real-time threat monitoring
- Device validation and tracking

For detailed authentication documentation, see [Account Authentication](docs/account-authentication.md)

#### Session Management
- User session tracking and state management
- Multi-device session coordination
- Token refresh mechanisms (10-minute refresh cycle)
- Session expiration and invalidation
- Online/offline state tracking
- Last seen timestamp management
- Force logout capabilities across devices

#### Cache Management
- Redis cache for distributed data:
  - Session data
  - Profile information
  - Security counters and blacklists
- Local cache for time-sensitive data:
  - Verification codes
  - Temporary tokens
  - Request tracking
- TTL-based cache invalidation
- Cache synchronization across nodes

#### Settings Management
- Versioned configuration with Redis caching
- Business and place-level settings hierarchy
- Real-time settings synchronization via Event Bus
- Settings change notifications across nodes
- App version and security configuration controls
For more details, see [Settings Management](#settings-management)

## User Account Management

### Registration and Verification
- Mobile Number Registration
  - International phone number support
  - Carrier and country validation
  - Device-based registration tracking
  - Installation ID management
- Verification System
  - Unified verification code and initial password
  - Multi-channel delivery through Messaging Service
  - Expiration controls and retry limits
  - Verification flow tracking

### Profile Management
- User Information
  - Mobile number management with verification
  - Password management with configurable expiration periods
  - Language and region preferences
  - Custom display settings
  - Communication preferences for messaging channels
- Account Status
  - Active/Inactive state management
  - Deactivation and reactivation flows
  - Account deletion (soft delete)
- Installation Management
  - Multi-device tracking
  - Device status monitoring
  - Installation history

### Data Services
- User Preferences
  - Card display preferences (layout, order, visibility)
  - Notification settings by channel (SMS, push, email)
  - Language and region settings
  - Custom card face options (themes, colors, designs)
  - Display settings (dark mode, font size, accessibility)
  - Messaging preferences by purpose
  - Security settings (biometric, session duration)
  - Offline data sync preferences
- Cache Management
  - Profile data caching with TTL
  - Preference caching with local persistence
  - Cache invalidation on profile updates
  - Sync mechanisms for offline data
  - Distributed cache with Redis
- Metrics and Monitoring
  - Login/logout tracking
  - Account activity monitoring
  - Message delivery tracking
  - Error tracking and reporting

### Localization
- Multi-language Support
  - English (en)
  - Simplified Chinese (zh-Hans)
  - Traditional Chinese (zh-Hant, zh-Hant-HK)
  - Korean (ko)
  - Japanese (ja)
  - Malay (ms)
  - Indonesian (id)
- Region-specific Features
  - Country-based verification methods
  - Regional security policies
  - Localized messaging templates
  - Channel availability by region


## Verification System
```mermaid
sequenceDiagram
	participant App as Perkd App
	participant AS as Account Service
	participant MS as Messaging Service
	participant Cognito as AWS Cognito
	participant Cache as Cache Layer

	App->>AS: Request Verification Code
	AS->>AS: Validate Request & Rate Limits
	AS->>Cache: Check Previous Attempts
	Cache-->>AS: Return Attempt Count
	
	alt Too Many Attempts
		AS-->>App: Return Rate Limit Error
	else Valid Request
		AS->>AS: Generate Verification Code
		AS->>Cache: Store Code & Update Attempts
		AS->>MS: Send Code via Selected Channel
		
		alt SMS Channel
			MS->>MS: Format SMS Message
			MS-->>App: Send SMS to User
		else WhatsApp Channel
			MS->>MS: Format WhatsApp Message
			MS-->>App: Send WhatsApp Message
		else Voice Channel
			MS->>MS: Prepare Voice Script
			MS-->>App: Make Voice Call
		end
		
		AS-->>App: Return Success Response
		
		Note over App,AS: User receives and enters code
		
		App->>AS: Submit Verification Code
		AS->>Cache: Validate Code & Expiry
		
		alt Invalid or Expired Code
			AS-->>App: Return Validation Error
		else Valid Code
			AS->>Cognito: Create/Update User Token
			Cognito-->>AS: Return JWT Token
			AS->>Cache: Mark Code as Used
			AS-->>App: Return Success with Token
		end
	end
```

### Code Generation & Management
- Verification codes are generated with configurable length (default set in settings)
- Codes are used for multiple purposes:
  - Account registration (also serves as initial account password)
  - Password reset/recovery
  - Mobile number change verification
- Code validity:
  - Validity period is purpose-specific and configurable
  - Codes expire based on `validitySeconds` setting
  - Used codes are marked with `usedAt` timestamp
  - Multiple active codes can exist simultaneously for different purposes

### Verification Channels
- Primary channels:
  - SMS (default)
  - WhatsApp (for supported countries)
  - Voice call (for specific country codes)
  - Email (for password reset only)
  - Push notifications (for password reset only)
- Channel selection:
  - Default channel is SMS
  - Channel preference is country-specific via `verificationChannel` settings
  - WhatsApp is used if specified and supported for the country
  - Voice calls are available as fallback for supported countries
  - Multiple channels may be used simultaneously for password reset

### Rate Limiting and Security
- Daily limits per purpose:
  - Registration: 5 attempts per day (configurable per country)
  - Password reset: 5 requests per day
  - Mobile number change: 5 verification attempts per day
- Whitelisting system:
  - Whitelisted countries have higher limits (up to 1000 attempts)
  - Country-specific limits can be configured
- Anti-fraud measures:
  - Device fingerprinting for suspicious activity
  - IP validation
  - Android version restrictions (v8.0+ required for non-whitelisted countries)
  - Blacklist system for suspicious devices/IPs

### Localization
- Multi-language support for verification messages:
  - English (en)
  - Traditional Chinese (zh-Hant)
  - Simplified Chinese (zh-Hans)
  - Hong Kong Chinese (zh-HK)
  - Japanese (ja)
  - Korean (ko)
  - Indonesian (id)
  - Malay (ms)
- Country-specific message templates
- Voice call scripts with language-specific prosody and timing


## Authentication & Authorization

### Authentication Flows
- **Mobile + Password Authentication**
  - Mobile number validation with country code check
  - Password verification with fallback to verification code
  - Automatic password update when logging in with verification code
  - Person profile data included in login response

- **Biometric Authentication**
  - Supports TouchID/FaceID with persistent JWT tokens
  - No password verification required
  - Device-specific token management
  - Biometric-specific event tracking

- **Account ID Authentication**
  - Used for app upgrades and migrations
  - Special handling for iOS app versions 4.0-4.0.2

### Session Management
- Token refresh mechanisms (10-minute refresh cycle)
- Force logout capabilities across devices
- Multi-device session handling with installation tracking
- Online/offline/suspended state management
- Last seen timestamp tracking

### AWS Cognito Integration
- Identity pool management in ap-southeast-1 region
- Token-based authentication (4200s duration)
- Secure access control with JWT
- Biometric authentication support (TouchID/FaceID)


## Settings Management
- **Settings Model**
  - Versioned settings with major/minor versioning
  - App-specific settings with `appId` tracking
  - Flexible data storage with JSON objects
  - Timestamp tracking (created, modified, deleted)
  - Redis-backed caching for performance

- **Settings Hierarchy**
  - Business-level settings inheritance
  - Place-level settings overrides
  - Multi-tenant settings management
  - Settings synchronization across services

- **Configuration Management**
  - App version management for iOS/Android
  - AWS Cognito configuration
  - Country whitelist/blacklist management
  - Device blacklist management
  - Error code configuration
  - Messaging templates and localization

- **Settings Synchronization**
  - Redis cache synchronization
  - Multi-node settings distribution
  - Real-time settings updates
  - Settings event hooks and triggers


## Anti-hacking

### Brute force protection with rate limiting
  - SMS verification through Messaging Service: 5 attempts per day (configurable per country)
  - Password attempts: 5 attempts before lockout
  - Mobile number change limits
  - Whitelisted numbers: 1000 attempts allowed

### Device security
  - IP address validation and request origin verification
  - Device fingerprinting and validation
  - Device ID verification
  - Android version restrictions (v8.0+ requirement for non-whitelisted countries)

### Access control
  - Blacklist/whitelist system for countries and carriers
  - Device model restrictions
  - Installation ID tracking
  - JWT token validation and refresh controls

### Monitoring and prevention
  - Suspicious activity detection and blocking
  - Real-time security event logging
  - Automated blocking of known malicious IPs
  - Regular security audits and penetration testing
  - Input sanitization and validation


## Service Integration
This service integrates with multiple microservices and external providers through APIs and events:

```mermaid
graph TB
    subgraph Account Service
        subgraph API Layer
            %% App APIs
            Auth_API[Auth APIs]
            Profile_API[Profile APIs]
            Device_API[Device APIs]
            Sync_API[Sync APIs]
            
            %% Admin APIs
            Admin_API[Admin APIs]
            Support_API[Support APIs]
            State_API[State Management APIs]
            Upgrade_API[Upgrade APIs]
        end

        subgraph Account Management
            Profile[Profile Manager]:::darkBlue
            Session[Session Manager]:::darkBlue
            Security[Security Controller]:::darkBlue
        end
        
        %% Core Components
        Prefs[Preference Management]:::darkBlue
        Sync[Sync Engine]
        
        %% Internal API Connections
        Auth_API --> Security
        Profile_API --> Profile
        Device_API --> Session
        Sync_API --> Sync
        Admin_API --> Profile
        Support_API --> Security
        State_API --> Profile
        Upgrade_API --> Sync
        
        %% Internal Service Connections
        Profile --> Prefs
        Profile --> Sync
        Session --> Security
        Session --> Sync
    end

    subgraph External Services
        PS[Person Service]
        IS[Installation Service]
        MSG[Messaging Service]
    end

    %% Infrastructure Components
    Cognito[AWS Cognito]
    EventBus[Event Bus]

    %% Client Applications
    App[Perkd App]:::perkdApp
    Admin[Admin & Support Portal]

    %% App to API Connections
    App --> Auth_API
    App --> Profile_API
    App --> Device_API
    App --> Sync_API
    App --> Upgrade_API

    %% Admin Portal Connections
    Admin --> Admin_API
    Admin --> Support_API
    Admin --> State_API

    %% External Service Connections
    Profile --> PS
    Session --> IS
    Profile --> MSG
    Security --> MSG

    %% Infrastructure Connections
    Security --> Cognito
    Profile --> EventBus
    Session --> EventBus

    %% Messaging Channels
    MSG --> Users((Users))

    classDef perkdApp fill:#B8860B,stroke:#000,stroke-width:2px
    classDef darkBlue fill:#00008B,stroke:#000,stroke-width:2px
```

- **Person Service**
	- Manages user profile data and personal information
	- Receives profile updates from Account service
	- Provides user data synchronization endpoints

- **Installation Service**
	- Handles device registration and tracking
	- Manages installation lifecycle and device state
	- Provides device validation endpoints

- **Messaging Service**
	- Delivers verification codes and password resets
	- Handles multi-channel communication (SMS, email, push)
	- Provides message templating and localization

- **AWS Cognito**
	- Manages user authentication and token generation
	- Handles secure password storage and validation
	- Provides JWT token verification endpoints


## Data Models
```mermaid
erDiagram
	Account {
		string mobileNumber PK
		string password
		string verificationCode
		string status
		date lastLoginAt
		date lastSeenAt
		json relations
		string[] phoneNumbers
		string[] emails
		string locale
		boolean isActive
		date verificationExpiry
		date passwordExpiry
		json deviceTracking
	}
	Person {
		string id PK
		string[] phoneNumbers
		string[] emails
		string locale
		json permissions
		json geolocation
	}
	Installation {
		string id PK
		string deviceModel
		string os
		string carrier
		json capabilities
		string state
		json geolocation
	}
	Preference {
		string accountId PK
		json cardDisplay
		json notifications
		string language
		string region
		json cardFaceOptions
		json displaySettings
		json customCardFace
		json offlineData
		json syncSettings
		json securitySettings
		json messagingPrefs
	}
	Settings {
		string name PK
		string appId
		json version
		string kind
		json data
		date createdAt
		date modifiedAt
		date deletedAt
	}

	Account ||--o{ Installation : "has"
	Account ||--|| Person : "has"
	Account ||--|| Preference : "has"
	Person ||--o{ Installation : "uses"
	Account ||--o{ Settings : "configures"
	Settings ||--o{ Installation : "applies_to"
```

### Key Entities

#### Account
- Core user account entity
- Primary identifier: Mobile number
- Manages authentication credentials
- Tracks account status and activity
- Stores verification and security data
- Handles device tracking information

#### Preference
- User-specific settings and preferences
- Card display configurations
- Notification preferences
- Regional and language settings
- Custom display options
- Security and sync settings

#### Person
- User profile information
- Contains contact details (phone numbers, emails)
- Manages user permissions
- Stores geolocation data

#### Installation
- Device and session management
- Tracks device specifications and capabilities
- Monitors carrier information
- Manages device state
- Stores geolocation data

#### Settings
- Core configuration entity
- Primary identifier: Name (string, max 32 chars)
- Manages versioned configurations
- Stores app-specific settings
- Handles multi-tenant data


### Key Relationships
- Account-Person: One-to-One
  - Each account is associated with exactly one person profile
  - Person profile contains extended user information

- Account-Installation: One-to-Many
  - One account can have multiple device installations
  - Each installation is linked to a specific account

- Account-Preference: One-to-One
  - Each account has one set of preferences
  - Preferences store user-specific settings


## API Endpoints

### Perkd App Endpoints
#### User Management
- `POST /Accounts/app/register` - Register new account
- `POST /Accounts/app/delete` - Delete account

#### Authentication & Security
- `POST /Accounts/app/login` - User login
- `POST /Accounts/app/logout` - User logout
- `POST /Accounts/app/verify` - Verify account
- `GET /Accounts/app/verify` - Request verification code
- `GET /Accounts/app/password` - Request password reset
- `POST /Accounts/app/password` - Change password
- `POST /Accounts/app/mobile` - Change account mobile number
- `POST /Accounts/app/mobile/verify` - Verify mobile number change
- `POST /Accounts/app/token/refresh` - Refresh authentication token
- `POST /Accounts/app/launch` - Launch app and get session token

#### Passkey Management
- `POST /Accounts/auth/register-passkey` - Get passkey registration options
- `POST /Accounts/auth/register-passkey/verify` - Verify passkey registration
- `POST /Accounts/auth/authenticate-passkey` - Get passkey authentication options
- `POST /Accounts/auth/authenticate-passkey/verify` - Verify passkey authentication
- `GET /Accounts/auth/passkeys` - List registered passkeys
- `DELETE /Accounts/auth/passkeys/:id` - Remove a passkey

#### Device Management
- `GET /Accounts/app/installations` - List account installations
- `POST /Accounts/app/installations` - Add new installation
- `DELETE /Accounts/app/installations/{installationId}` - Remove installation

#### Settings Management
- `GET /settings/:name` - Get settings by name
- `POST /settings/:name` - Update settings
- `DELETE /settings/:name` - Remove settings
- `POST /settings/redis/sync` - Sync settings to Redis cache
- `POST /settings` - Add new settings
- `POST /settings/:name/update` - Update specific settings with handlers

### Admin & Customer Support

#### Account State Management
- `POST /Accounts/{id}/activate` - Activate account
- `POST /Accounts/{id}/deactivate` - Deactivate account
- `POST /Accounts/{id}/resetCache` - Reset account cache
- `POST /Accounts/delete` - Soft delete account

#### Customer Support
- `POST /Accounts/sendPassword` - Send password to account (auth assist)
- `POST /Accounts/vcode` - Create new verification code
- `POST /Accounts/createMessage` - Send verification code message
- `POST /Accounts/migrate` - Migrate account to different person

### Sync Engine
- `POST /Accounts/app/sync` - Synchronize data between app and server
- `POST /Accounts/app/fetch` - Fetch specific objects by ID


## Events

### Published Events
Events emitted by this service for other services to consume.

#### Authentication Events
- `xaccount.account.registered` - Emitted when a new account is registered
- `xaccount.account.error.login` - Emitted on login errors
- `xaccount.account.launched` - Emitted when account service is launched
- `xaccount.account.notify` - Emitted for account notifications/messaging

#### Account State Events
- `xaccount.account.deactivate` - Emitted when an account is deactivated
- `xaccount.account.activate` - Emitted when a deactivated account is reactivated
- `xaccount.account.forceLogout` - Emitted when force logout is triggered for an account
- `xaccount.account.delete` - Emitted when an account is deleted


### Subscribed Events
Events consumed by this service from other services.

#### Person Service
- `person.person.updated` - Handles updates to person data (e.g. mobile number changes)

#### Installation Service
- `sync.app.app.launch.offline` - Handles offline app launch events
